# Core GUI Framework
PyQt5==5.15.10
PyQt5-Qt5==5.15.2
PyQt5-sip==12.13.0

# Alternative GUI Framework (uncomment if using Tkinter instead)
# tkinter is built-in with Python, no need to install

# Database
sqlite3  # Built-in with Python

# Data Processing and Analysis
pandas==2.1.4
numpy==1.24.4

# Date and Time Handling
python-dateutil==2.8.2

# PDF Generation for Reports
reportlab==4.0.7
fpdf2==2.7.6

# Excel Export
openpyxl==3.1.2
xlsxwriter==3.1.9

# Barcode Generation and Reading
python-barcode==0.15.1
qrcode==7.4.2
Pillow==10.1.0

# Arabic Text Processing
python-bidi==0.4.2
arabic-reshaper==3.0.0

# Configuration Management
configparser  # Built-in with Python
json  # Built-in with Python

# Logging
logging  # Built-in with Python

# Validation
cerberus==1.3.5

# Encryption for passwords
bcrypt==4.1.2
cryptography==41.0.8

# System Integration
psutil==5.9.6

# Development and Testing
pytest==7.4.3
pytest-qt==4.2.0

# Packaging (for creating executable)
pyinstaller==6.3.0
auto-py-to-exe==2.41.0

# Additional utilities
requests==2.31.0
