# -*- coding: utf-8 -*-
"""
دوال مساعدة عامة
General Helper Functions

يحتوي على دوال مساعدة عامة للتطبيق
Contains general helper functions for the application
"""

import os
import sys
import logging
import platform
import psutil
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, List
from PyQt5.QtCore import QStandardPaths

def setup_logging(log_level: str = "INFO") -> None:
    """
    إعداد نظام السجلات
    Setup logging system
    """
    # إنشاء مجلد السجلات
    # Create logs directory
    logs_dir = Path(QStandardPaths.writableLocation(
        QStandardPaths.AppDataLocation
    )) / "TruckPartsManagement" / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # تحديد مسار ملف السجل
    # Determine log file path
    log_file = logs_dir / f"truck_parts_{datetime.now().strftime('%Y%m%d')}.log"
    
    # إعداد التنسيق
    # Setup formatting
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد معالج الملف
    # Setup file handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # إعداد معالج وحدة التحكم
    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # إعداد السجل الجذر
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    logging.info("تم إعداد نظام السجلات - Logging system initialized")

def check_system_requirements() -> bool:
    """
    فحص متطلبات النظام
    Check system requirements
    """
    try:
        # فحص إصدار Python
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            logging.error(f"إصدار Python غير مدعوم: {python_version} - Unsupported Python version")
            return False
        
        # فحص الذاكرة المتاحة
        # Check available memory
        memory = psutil.virtual_memory()
        if memory.available < 512 * 1024 * 1024:  # 512 MB
            logging.warning("ذاكرة منخفضة - Low memory warning")
        
        # فحص مساحة القرص
        # Check disk space
        disk = psutil.disk_usage('/')
        if disk.free < 1024 * 1024 * 1024:  # 1 GB
            logging.warning("مساحة قرص منخفضة - Low disk space warning")
        
        # فحص نظام التشغيل
        # Check operating system
        os_name = platform.system()
        logging.info(f"نظام التشغيل: {os_name} {platform.release()} - Operating System")
        
        return True
        
    except Exception as e:
        logging.error(f"خطأ في فحص متطلبات النظام - System requirements check error: {e}")
        return False

def format_currency(amount: float, currency: str = "DZD") -> str:
    """
    تنسيق العملة
    Format currency
    """
    try:
        if currency == "DZD":
            return f"{amount:,.2f} د.ج"
        elif currency == "USD":
            return f"${amount:,.2f}"
        elif currency == "EUR":
            return f"€{amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    except:
        return str(amount)

def format_date(date_str: str, input_format: str = "%Y-%m-%d", output_format: str = "%d/%m/%Y") -> str:
    """
    تنسيق التاريخ
    Format date
    """
    try:
        if not date_str:
            return ""
        
        if isinstance(date_str, str):
            date_obj = datetime.strptime(date_str, input_format)
        else:
            date_obj = date_str
        
        return date_obj.strftime(output_format)
    except:
        return str(date_str)

def format_phone(phone: str, country_code: str = "+213") -> str:
    """
    تنسيق رقم الهاتف
    Format phone number
    """
    try:
        if not phone:
            return ""
        
        # إزالة المسافات والرموز
        # Remove spaces and symbols
        clean_phone = ''.join(filter(str.isdigit, phone))
        
        # إضافة رمز الدولة إذا لم يكن موجوداً
        # Add country code if not present
        if not clean_phone.startswith(country_code.replace("+", "")):
            if clean_phone.startswith("0"):
                clean_phone = country_code.replace("+", "") + clean_phone[1:]
            else:
                clean_phone = country_code.replace("+", "") + clean_phone
        
        return f"+{clean_phone}"
    except:
        return phone

def generate_invoice_number(prefix: str = "INV", last_number: int = 0) -> str:
    """
    إنشاء رقم فاتورة
    Generate invoice number
    """
    try:
        year = datetime.now().year
        month = datetime.now().month
        new_number = last_number + 1
        return f"{prefix}-{year}{month:02d}-{new_number:06d}"
    except:
        return f"{prefix}-{datetime.now().strftime('%Y%m%d')}-000001"

def generate_barcode(part_number: str, category_id: int = 1) -> str:
    """
    إنشاء باركود
    Generate barcode
    """
    try:
        # تنسيق بسيط للباركود
        # Simple barcode format
        timestamp = datetime.now().strftime("%y%m%d")
        return f"{category_id:02d}{timestamp}{hash(part_number) % 10000:04d}"
    except:
        return f"000{datetime.now().strftime('%y%m%d')}0001"

def calculate_age_in_days(date_str: str) -> int:
    """
    حساب العمر بالأيام
    Calculate age in days
    """
    try:
        if not date_str:
            return 0
        
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        today = datetime.now()
        return (today - date_obj).days
    except:
        return 0

def is_overdue(due_date: str, days_grace: int = 0) -> bool:
    """
    فحص التأخير
    Check if overdue
    """
    try:
        if not due_date:
            return False
        
        due_date_obj = datetime.strptime(due_date, "%Y-%m-%d")
        grace_date = due_date_obj + timedelta(days=days_grace)
        return datetime.now() > grace_date
    except:
        return False

def sanitize_filename(filename: str) -> str:
    """
    تنظيف اسم الملف
    Sanitize filename
    """
    try:
        # إزالة الأحرف غير المسموحة
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # تحديد الطول الأقصى
        # Limit length
        if len(filename) > 255:
            filename = filename[:255]
        
        return filename.strip()
    except:
        return "file"

def get_file_size_mb(file_path: str) -> float:
    """
    الحصول على حجم الملف بالميجابايت
    Get file size in MB
    """
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except:
        return 0.0

def create_directory_if_not_exists(directory_path: str) -> bool:
    """
    إنشاء مجلد إذا لم يكن موجوداً
    Create directory if it doesn't exist
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"خطأ في إنشاء المجلد - Directory creation error: {e}")
        return False

def backup_file(source_path: str, backup_dir: str) -> Optional[str]:
    """
    إنشاء نسخة احتياطية من ملف
    Create backup of a file
    """
    try:
        source = Path(source_path)
        if not source.exists():
            return None
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{source.stem}_{timestamp}{source.suffix}"
        backup_file_path = backup_path / backup_filename
        
        import shutil
        shutil.copy2(source, backup_file_path)
        
        logging.info(f"تم إنشاء نسخة احتياطية - Backup created: {backup_file_path}")
        return str(backup_file_path)
        
    except Exception as e:
        logging.error(f"خطأ في إنشاء النسخة الاحتياطية - Backup creation error: {e}")
        return None

def validate_email(email: str) -> bool:
    """
    التحقق من صحة البريد الإلكتروني
    Validate email address
    """
    try:
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    except:
        return False

def validate_phone(phone: str) -> bool:
    """
    التحقق من صحة رقم الهاتف
    Validate phone number
    """
    try:
        # إزالة المسافات والرموز
        # Remove spaces and symbols
        clean_phone = ''.join(filter(str.isdigit, phone.replace("+", "")))
        
        # فحص الطول
        # Check length
        return 8 <= len(clean_phone) <= 15
    except:
        return False

def get_system_info() -> Dict[str, Any]:
    """
    الحصول على معلومات النظام
    Get system information
    """
    try:
        return {
            "platform": platform.platform(),
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_total": psutil.disk_usage('/').total,
            "disk_free": psutil.disk_usage('/').free,
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1)
        }
    except Exception as e:
        logging.error(f"خطأ في الحصول على معلومات النظام - System info error: {e}")
        return {}

def convert_to_arabic_numerals(text: str) -> str:
    """
    تحويل الأرقام إلى العربية
    Convert numbers to Arabic numerals
    """
    try:
        arabic_numerals = "٠١٢٣٤٥٦٧٨٩"
        english_numerals = "0123456789"
        
        for i, english_num in enumerate(english_numerals):
            text = text.replace(english_num, arabic_numerals[i])
        
        return text
    except:
        return text

def convert_to_english_numerals(text: str) -> str:
    """
    تحويل الأرقام إلى الإنجليزية
    Convert numbers to English numerals
    """
    try:
        arabic_numerals = "٠١٢٣٤٥٦٧٨٩"
        english_numerals = "0123456789"
        
        for i, arabic_num in enumerate(arabic_numerals):
            text = text.replace(arabic_num, english_numerals[i])
        
        return text
    except:
        return text
