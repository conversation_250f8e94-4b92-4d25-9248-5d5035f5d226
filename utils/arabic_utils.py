# -*- coding: utf-8 -*-
"""
أدوات دعم اللغة العربية
Arabic Language Support Utilities

يحتوي على دوال لدعم النصوص العربية والتخطيط من اليمين لليسار
Contains functions for Arabic text support and RTL layout
"""

import logging
from typing import Optional
from PyQt5.QtCore import Qt, QLocale
from PyQt5.QtGui import QFont, QFontDatabase
from PyQt5.QtWidgets import QApplication

try:
    import arabic_reshaper
    import bidi.algorithm
    ARABIC_SUPPORT_AVAILABLE = True
except ImportError:
    ARABIC_SUPPORT_AVAILABLE = False
    logging.warning("مكتبات دعم العربية غير متوفرة - Arabic support libraries not available")

def setup_arabic_support():
    """
    إعداد دعم اللغة العربية
    Setup Arabic language support
    """
    try:
        # تعيين اتجاه التخطيط
        # Set layout direction
        app = QApplication.instance()
        if app:
            app.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين اللغة المحلية
        # Set locale
        QLocale.setDefault(QLocale(QLocale.Arabic, QLocale.Algeria))
        
        # تحميل الخطوط العربية
        # Load Arabic fonts
        load_arabic_fonts()
        
        logging.info("تم إعداد دعم اللغة العربية - Arabic language support initialized")
        
    except Exception as e:
        logging.error(f"خطأ في إعداد دعم العربية - Arabic support setup error: {e}")

def load_arabic_fonts():
    """
    تحميل الخطوط العربية
    Load Arabic fonts
    """
    try:
        font_db = QFontDatabase()
        
        # قائمة الخطوط العربية المفضلة
        # List of preferred Arabic fonts
        preferred_fonts = [
            "Segoe UI",
            "Tahoma", 
            "Arial Unicode MS",
            "DejaVu Sans",
            "Liberation Sans",
            "Noto Sans Arabic",
            "Amiri",
            "Scheherazade"
        ]
        
        # البحث عن الخطوط المتاحة
        # Search for available fonts
        available_fonts = font_db.families()
        
        for font_name in preferred_fonts:
            if font_name in available_fonts:
                font = QFont(font_name, 10)
                app = QApplication.instance()
                if app:
                    app.setFont(font)
                logging.info(f"تم تحميل الخط العربي: {font_name} - Arabic font loaded")
                break
        
    except Exception as e:
        logging.error(f"خطأ في تحميل الخطوط العربية - Arabic fonts loading error: {e}")

def reshape_arabic_text(text: str) -> str:
    """
    إعادة تشكيل النص العربي
    Reshape Arabic text for proper display
    """
    try:
        if not ARABIC_SUPPORT_AVAILABLE or not text:
            return text
        
        # إعادة تشكيل النص العربي
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        
        # تطبيق خوارزمية الاتجاه الثنائي
        # Apply bidirectional algorithm
        bidi_text = bidi.algorithm.get_display(reshaped_text)
        
        return bidi_text
        
    except Exception as e:
        logging.error(f"خطأ في إعادة تشكيل النص العربي - Arabic text reshaping error: {e}")
        return text

def is_arabic_text(text: str) -> bool:
    """
    فحص ما إذا كان النص يحتوي على أحرف عربية
    Check if text contains Arabic characters
    """
    try:
        if not text:
            return False
        
        # نطاق الأحرف العربية في Unicode
        # Arabic Unicode range
        arabic_range = range(0x0600, 0x06FF + 1)
        
        for char in text:
            if ord(char) in arabic_range:
                return True
        
        return False
        
    except Exception as e:
        logging.error(f"خطأ في فحص النص العربي - Arabic text detection error: {e}")
        return False

def get_text_direction(text: str) -> Qt.LayoutDirection:
    """
    تحديد اتجاه النص
    Determine text direction
    """
    try:
        if is_arabic_text(text):
            return Qt.RightToLeft
        else:
            return Qt.LeftToRight
    except:
        return Qt.RightToLeft  # افتراضي للعربية - Default to Arabic

def format_arabic_number(number: float, decimal_places: int = 2) -> str:
    """
    تنسيق الأرقام للعرض العربي
    Format numbers for Arabic display
    """
    try:
        # تنسيق الرقم
        # Format number
        formatted = f"{number:,.{decimal_places}f}"
        
        # تحويل الأرقام إلى العربية إذا لزم الأمر
        # Convert to Arabic numerals if needed
        arabic_numerals = "٠١٢٣٤٥٦٧٨٩"
        english_numerals = "0123456789"
        
        for i, english_num in enumerate(english_numerals):
            formatted = formatted.replace(english_num, arabic_numerals[i])
        
        return formatted
        
    except Exception as e:
        logging.error(f"خطأ في تنسيق الرقم العربي - Arabic number formatting error: {e}")
        return str(number)

def get_arabic_font(size: int = 10, bold: bool = False) -> QFont:
    """
    الحصول على خط عربي
    Get Arabic font
    """
    try:
        # قائمة الخطوط العربية بترتيب الأولوية
        # List of Arabic fonts in priority order
        font_families = [
            "Segoe UI",
            "Tahoma",
            "Arial Unicode MS", 
            "DejaVu Sans",
            "Liberation Sans"
        ]
        
        for family in font_families:
            font = QFont(family, size)
            if bold:
                font.setBold(True)
            
            # فحص ما إذا كان الخط متاحاً
            # Check if font is available
            if font.exactMatch():
                return font
        
        # خط افتراضي
        # Default font
        font = QFont("Arial", size)
        if bold:
            font.setBold(True)
        return font
        
    except Exception as e:
        logging.error(f"خطأ في الحصول على الخط العربي - Arabic font error: {e}")
        return QFont("Arial", size)

def create_rtl_layout_widget():
    """
    إنشاء عنصر واجهة بتخطيط من اليمين لليسار
    Create RTL layout widget
    """
    from PyQt5.QtWidgets import QWidget
    
    widget = QWidget()
    widget.setLayoutDirection(Qt.RightToLeft)
    return widget

def set_widget_rtl(widget):
    """
    تعيين عنصر الواجهة للتخطيط من اليمين لليسار
    Set widget to RTL layout
    """
    try:
        widget.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الخط العربي
        # Apply Arabic font
        arabic_font = get_arabic_font()
        widget.setFont(arabic_font)
        
    except Exception as e:
        logging.error(f"خطأ في تعيين التخطيط العربي - RTL layout setting error: {e}")

def translate_text(text: str, translations: dict) -> str:
    """
    ترجمة النص باستخدام قاموس الترجمات
    Translate text using translations dictionary
    """
    try:
        return translations.get(text, text)
    except:
        return text

def get_arabic_date_format() -> str:
    """
    الحصول على تنسيق التاريخ العربي
    Get Arabic date format
    """
    return "dd/MM/yyyy"

def get_arabic_time_format() -> str:
    """
    الحصول على تنسيق الوقت العربي
    Get Arabic time format
    """
    return "hh:mm:ss"

def format_arabic_currency(amount: float, currency: str = "د.ج") -> str:
    """
    تنسيق العملة بالعربية
    Format currency in Arabic
    """
    try:
        # تنسيق الرقم
        # Format number
        formatted_amount = format_arabic_number(amount, 2)
        
        # إضافة رمز العملة
        # Add currency symbol
        return f"{formatted_amount} {currency}"
        
    except Exception as e:
        logging.error(f"خطأ في تنسيق العملة العربية - Arabic currency formatting error: {e}")
        return f"{amount} {currency}"

def get_arabic_month_names() -> list:
    """
    الحصول على أسماء الأشهر بالعربية
    Get Arabic month names
    """
    return [
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]

def get_arabic_day_names() -> list:
    """
    الحصول على أسماء الأيام بالعربية
    Get Arabic day names
    """
    return [
        "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", 
        "الخميس", "الجمعة", "السبت"
    ]

def validate_arabic_input(text: str) -> bool:
    """
    التحقق من صحة الإدخال العربي
    Validate Arabic input
    """
    try:
        if not text:
            return True
        
        # السماح بالأحرف العربية والأرقام والمسافات وعلامات الترقيم
        # Allow Arabic letters, numbers, spaces, and punctuation
        allowed_chars = set()
        
        # الأحرف العربية
        # Arabic letters
        for i in range(0x0600, 0x06FF + 1):
            allowed_chars.add(chr(i))
        
        # الأرقام العربية والإنجليزية
        # Arabic and English numbers
        for i in range(ord('0'), ord('9') + 1):
            allowed_chars.add(chr(i))
        for i in range(ord('٠'), ord('٩') + 1):
            allowed_chars.add(chr(i))
        
        # المسافات وعلامات الترقيم
        # Spaces and punctuation
        allowed_chars.update([' ', '.', ',', '!', '?', ':', ';', '-', '_', '(', ')', '[', ']'])
        
        for char in text:
            if char not in allowed_chars:
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"خطأ في التحقق من الإدخال العربي - Arabic input validation error: {e}")
        return True  # السماح بالإدخال في حالة الخطأ - Allow input on error
