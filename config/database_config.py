# -*- coding: utf-8 -*-
"""
إعدادات قاعدة البيانات
Database Configuration Module

يحتوي على جميع إعدادات قاعدة البيانات والاستعلامات
Contains all database settings and queries
"""

import os
from pathlib import Path

class DatabaseConfig:
    """
    فئة إعدادات قاعدة البيانات
    Database Configuration Class
    """
    
    # معلومات قاعدة البيانات
    # Database information
    DB_NAME = "truck_parts.db"
    DB_VERSION = "1.0"
    
    # إعدادات الاتصال
    # Connection settings
    CONNECTION_TIMEOUT = 30
    BUSY_TIMEOUT = 30000  # milliseconds
    
    # إعدادات الأداء
    # Performance settings
    PRAGMA_SETTINGS = {
        "foreign_keys": "ON",
        "journal_mode": "WAL",
        "synchronous": "NORMAL",
        "cache_size": "-64000",  # 64MB cache
        "temp_store": "MEMORY",
        "mmap_size": "268435456",  # 256MB mmap
    }
    
    # استعلامات إنشاء الجداول
    # Table creation queries
    CREATE_TABLES_SQL = """
    -- Enable foreign keys
    PRAGMA foreign_keys = ON;
    
    -- Users table (جدول المستخدمين)
    CREATE TABLE IF NOT EXISTS users (
        user_id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role TEXT NOT NULL CHECK(role IN ('admin', 'manager', 'employee')),
        email TEXT UNIQUE,
        phone TEXT,
        last_login TEXT,
        is_active INTEGER DEFAULT 1 CHECK(is_active IN (0, 1)),
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Brands table (جدول الماركات)
    CREATE TABLE IF NOT EXISTS brands (
        brand_id INTEGER PRIMARY KEY AUTOINCREMENT,
        brand_name TEXT NOT NULL UNIQUE,
        description TEXT,
        country_of_origin TEXT,
        website_url TEXT,
        logo_path TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Categories table (جدول الفئات)
    CREATE TABLE IF NOT EXISTS categories (
        category_id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_name TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_category_id INTEGER,
        category_image_path TEXT,
        display_order INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_category_id) REFERENCES categories(category_id) ON DELETE SET NULL
    );
    
    -- Suppliers table (جدول الموردين)
    CREATE TABLE IF NOT EXISTS suppliers (
        supplier_id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        supplier_rating INTEGER,
        average_lead_time_days INTEGER,
        payment_terms_agreed TEXT,
        bank_account_details TEXT,
        tax_id_number TEXT,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Customers table (جدول العملاء)
    CREATE TABLE IF NOT EXISTS customers (
        customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        customer_type TEXT CHECK(customer_type IN ('individual', 'company', 'workshop', 'fleet_owner')),
        loyalty_points INTEGER DEFAULT 0,
        last_purchase_date TEXT,
        total_spent_amount REAL DEFAULT 0.00,
        credit_limit REAL DEFAULT 0.00,
        tax_id_number TEXT,
        account_manager_id INTEGER,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_manager_id) REFERENCES users(user_id) ON DELETE SET NULL
    );
    """
    
    # فهارس قاعدة البيانات
    # Database indexes
    CREATE_INDEXES_SQL = """
    -- Performance indexes
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_brands_name ON brands(brand_name);
    CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(category_name);
    CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_category_id);
    CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(supplier_name);
    CREATE INDEX IF NOT EXISTS idx_suppliers_email ON suppliers(email);
    CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name);
    CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
    CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type);
    """
    
    # مشغلات قاعدة البيانات
    # Database triggers
    CREATE_TRIGGERS_SQL = """
    -- Update timestamp triggers
    CREATE TRIGGER IF NOT EXISTS trg_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE user_id = OLD.user_id;
    END;
    
    CREATE TRIGGER IF NOT EXISTS trg_brands_updated_at
    AFTER UPDATE ON brands
    FOR EACH ROW
    BEGIN
        UPDATE brands SET updated_at = CURRENT_TIMESTAMP WHERE brand_id = OLD.brand_id;
    END;
    
    CREATE TRIGGER IF NOT EXISTS trg_categories_updated_at
    AFTER UPDATE ON categories
    FOR EACH ROW
    BEGIN
        UPDATE categories SET updated_at = CURRENT_TIMESTAMP WHERE category_id = OLD.category_id;
    END;
    
    CREATE TRIGGER IF NOT EXISTS trg_suppliers_updated_at
    AFTER UPDATE ON suppliers
    FOR EACH ROW
    BEGIN
        UPDATE suppliers SET updated_at = CURRENT_TIMESTAMP WHERE supplier_id = OLD.supplier_id;
    END;
    
    CREATE TRIGGER IF NOT EXISTS trg_customers_updated_at
    AFTER UPDATE ON customers
    FOR EACH ROW
    BEGIN
        UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE customer_id = OLD.customer_id;
    END;
    """
    
    # بيانات افتراضية
    # Default data
    INSERT_DEFAULT_DATA_SQL = """
    -- Insert default admin user
    INSERT OR IGNORE INTO users (username, password_hash, full_name, role, email)
    VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoC', 'مدير النظام', 'admin', '<EMAIL>');
    
    -- Insert default categories
    INSERT OR IGNORE INTO categories (category_name, description, display_order)
    VALUES 
        ('محرك', 'قطع غيار المحرك', 1),
        ('فرامل', 'نظام الفرامل', 2),
        ('تعليق', 'نظام التعليق', 3),
        ('كهرباء', 'النظام الكهربائي', 4),
        ('هيكل', 'قطع الهيكل الخارجي', 5),
        ('داخلية', 'القطع الداخلية', 6);
    
    -- Insert default brands
    INSERT OR IGNORE INTO brands (brand_name, description, country_of_origin)
    VALUES 
        ('Mercedes-Benz', 'مرسيدس بنز', 'Germany'),
        ('Volvo', 'فولفو', 'Sweden'),
        ('Scania', 'سكانيا', 'Sweden'),
        ('MAN', 'مان', 'Germany'),
        ('DAF', 'داف', 'Netherlands'),
        ('Iveco', 'إيفيكو', 'Italy');
    """
    
    @classmethod
    def get_schema_file_path(cls) -> Path:
        """الحصول على مسار ملف المخطط - Get schema file path"""
        return Path(__file__).parent.parent / "database_schema_enhanced_sqlite.sql"
    
    @classmethod
    def get_backup_filename(cls, timestamp: str = None) -> str:
        """إنشاء اسم ملف النسخة الاحتياطية - Generate backup filename"""
        if timestamp is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"truck_parts_backup_{timestamp}.db"
    
    @classmethod
    def get_connection_string(cls, db_path: str) -> str:
        """إنشاء نص الاتصال بقاعدة البيانات - Generate database connection string"""
        return f"file:{db_path}?mode=rwc"
