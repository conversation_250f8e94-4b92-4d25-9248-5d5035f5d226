# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Settings Module

يدير جميع إعدادات التطبيق والتفضيلات
Manages all application settings and preferences
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from PyQt5.QtCore import QSettings, QStandardPaths

class AppSettings:
    """
    فئة إدارة إعدادات التطبيق
    Application Settings Manager Class
    """
    
    def __init__(self):
        """تهيئة إعدادات التطبيق - Initialize application settings"""
        self.app_name = "TruckPartsManagement"
        self.organization = "SparePartsSolutions"
        
        # إعداد مسارات التطبيق
        # Setup application paths
        self.setup_paths()
        
        # إعداد QSettings
        # Setup QSettings
        self.qsettings = QSettings(self.organization, self.app_name)
        
        # الإعدادات الافتراضية
        # Default settings
        self.default_settings = self.get_default_settings()
        
        # تحميل الإعدادات
        # Load settings
        self.load_settings()
    
    def setup_paths(self):
        """إعداد مسارات التطبيق - Setup application paths"""
        # مجلد البيانات
        # Data directory
        self.data_dir = Path(QStandardPaths.writableLocation(
            QStandardPaths.AppDataLocation
        )) / self.app_name
        
        # مجلد قاعدة البيانات
        # Database directory
        self.db_dir = self.data_dir / "database"
        
        # مجلد النسخ الاحتياطية
        # Backup directory
        self.backup_dir = self.data_dir / "backups"
        
        # مجلد التقارير
        # Reports directory
        self.reports_dir = self.data_dir / "reports"
        
        # مجلد السجلات
        # Logs directory
        self.logs_dir = self.data_dir / "logs"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        # Create directories if they don't exist
        for directory in [self.data_dir, self.db_dir, self.backup_dir, 
                         self.reports_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_default_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات الافتراضية - Get default settings"""
        return {
            # إعدادات عامة - General settings
            "general": {
                "language": "ar",  # Arabic by default
                "theme": "light",  # light or dark
                "auto_backup": True,
                "backup_interval_days": 7,
                "currency": "DZD",  # Algerian Dinar
                "tax_rate": 19.0,  # Default tax rate percentage
                "company_name": "شركة قطع غيار الشاحنات",
                "company_address": "",
                "company_phone": "",
                "company_email": "",
                "company_logo_path": ""
            },
            
            # إعدادات قاعدة البيانات - Database settings
            "database": {
                "path": str(self.db_dir / "truck_parts.db"),
                "auto_vacuum": True,
                "backup_on_startup": True,
                "connection_timeout": 30
            },
            
            # إعدادات المخزون - Inventory settings
            "inventory": {
                "low_stock_threshold": 5,
                "auto_reorder": False,
                "barcode_format": "CODE128",
                "show_zero_stock": False,
                "default_shelf_location": "A1-01"
            },
            
            # إعدادات المبيعات - Sales settings
            "sales": {
                "auto_invoice_number": True,
                "invoice_prefix": "INV",
                "default_payment_method": "cash",
                "print_after_sale": True,
                "allow_negative_stock": False,
                "default_discount_type": "percentage"
            },
            
            # إعدادات التقارير - Reports settings
            "reports": {
                "default_format": "pdf",
                "include_logo": True,
                "page_size": "A4",
                "date_format": "dd/MM/yyyy",
                "number_format": "arabic"
            },
            
            # إعدادات النافذة - Window settings
            "window": {
                "width": 1200,
                "height": 800,
                "maximized": False,
                "position_x": 100,
                "position_y": 100,
                "remember_size": True
            },
            
            # إعدادات التنبيهات - Alerts settings
            "alerts": {
                "low_stock_enabled": True,
                "overdue_payments_enabled": True,
                "backup_reminders_enabled": True,
                "sound_enabled": True,
                "notification_duration": 5000  # milliseconds
            },
            
            # إعدادات الطباعة - Printing settings
            "printing": {
                "default_printer": "",
                "paper_size": "A4",
                "orientation": "portrait",
                "margins": {
                    "top": 20,
                    "bottom": 20,
                    "left": 20,
                    "right": 20
                }
            }
        }
    
    def load_settings(self):
        """تحميل الإعدادات - Load settings"""
        try:
            # تحميل الإعدادات من QSettings
            # Load settings from QSettings
            self.settings = {}
            
            for category, defaults in self.default_settings.items():
                self.settings[category] = {}
                for key, default_value in defaults.items():
                    if isinstance(default_value, dict):
                        # للإعدادات المتداخلة
                        # For nested settings
                        self.settings[category][key] = {}
                        for sub_key, sub_default in default_value.items():
                            setting_key = f"{category}/{key}/{sub_key}"
                            self.settings[category][key][sub_key] = self.qsettings.value(
                                setting_key, sub_default
                            )
                    else:
                        setting_key = f"{category}/{key}"
                        self.settings[category][key] = self.qsettings.value(
                            setting_key, default_value
                        )
            
            logging.info("تم تحميل الإعدادات بنجاح - Settings loaded successfully")
            
        except Exception as e:
            logging.error(f"خطأ في تحميل الإعدادات - Error loading settings: {e}")
            self.settings = self.default_settings.copy()
    
    def save_settings(self):
        """حفظ الإعدادات - Save settings"""
        try:
            for category, category_settings in self.settings.items():
                for key, value in category_settings.items():
                    if isinstance(value, dict):
                        # للإعدادات المتداخلة
                        # For nested settings
                        for sub_key, sub_value in value.items():
                            setting_key = f"{category}/{key}/{sub_key}"
                            self.qsettings.setValue(setting_key, sub_value)
                    else:
                        setting_key = f"{category}/{key}"
                        self.qsettings.setValue(setting_key, value)
            
            self.qsettings.sync()
            logging.info("تم حفظ الإعدادات بنجاح - Settings saved successfully")
            
        except Exception as e:
            logging.error(f"خطأ في حفظ الإعدادات - Error saving settings: {e}")
    
    def get(self, category: str, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد - Get setting value"""
        try:
            return self.settings.get(category, {}).get(key, default)
        except:
            return default
    
    def set(self, category: str, key: str, value: Any):
        """تعيين قيمة إعداد - Set setting value"""
        if category not in self.settings:
            self.settings[category] = {}
        self.settings[category][key] = value
    
    def get_nested(self, category: str, key: str, sub_key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد متداخل - Get nested setting value"""
        try:
            return self.settings.get(category, {}).get(key, {}).get(sub_key, default)
        except:
            return default
    
    def set_nested(self, category: str, key: str, sub_key: str, value: Any):
        """تعيين قيمة إعداد متداخل - Set nested setting value"""
        if category not in self.settings:
            self.settings[category] = {}
        if key not in self.settings[category]:
            self.settings[category][key] = {}
        self.settings[category][key][sub_key] = value
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية - Reset settings to defaults"""
        self.settings = self.default_settings.copy()
        self.save_settings()
        logging.info("تم إعادة تعيين الإعدادات للقيم الافتراضية - Settings reset to defaults")
    
    def export_settings(self, file_path: str) -> bool:
        """تصدير الإعدادات إلى ملف - Export settings to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            logging.info(f"تم تصدير الإعدادات إلى {file_path} - Settings exported to {file_path}")
            return True
        except Exception as e:
            logging.error(f"خطأ في تصدير الإعدادات - Error exporting settings: {e}")
            return False
    
    def import_settings(self, file_path: str) -> bool:
        """استيراد الإعدادات من ملف - Import settings from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # دمج الإعدادات المستوردة مع الحالية
            # Merge imported settings with current ones
            for category, category_settings in imported_settings.items():
                if category in self.settings:
                    self.settings[category].update(category_settings)
                else:
                    self.settings[category] = category_settings
            
            self.save_settings()
            logging.info(f"تم استيراد الإعدادات من {file_path} - Settings imported from {file_path}")
            return True
            
        except Exception as e:
            logging.error(f"خطأ في استيراد الإعدادات - Error importing settings: {e}")
            return False
