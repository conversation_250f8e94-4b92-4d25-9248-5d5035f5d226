export interface ElectronAPI {
    dbQuery: (sql: string, params?: any[]) => Promise<any[]>;
    dbRun: (sql: string, params?: any[]) => Promise<any>;
    selectFile: () => Promise<string | null>;
    saveFile: (content: string, filename: string) => Promise<boolean>;
    getVersion: () => string;
    getPlatform: () => string;
}
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
//# sourceMappingURL=preload.d.ts.map