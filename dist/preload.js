"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// كشف API للعملية الرئيسية
const electronAPI = {
    // عمليات قاعدة البيانات
    dbQuery: (sql, params) => electron_1.ipcRenderer.invoke('db-query', sql, params),
    dbRun: (sql, params) => electron_1.ipcRenderer.invoke('db-run', sql, params),
    // عمليات الملفات
    selectFile: () => electron_1.ipcRenderer.invoke('select-file'),
    saveFile: (content, filename) => electron_1.ipcRenderer.invoke('save-file', content, filename),
    // معلومات النظام
    getVersion: () => process.versions.electron,
    getPlatform: () => process.platform,
};
// كشف API للنافذة
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
//# sourceMappingURL=preload.js.map