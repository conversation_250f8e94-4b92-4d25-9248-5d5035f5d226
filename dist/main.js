"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const sqlite3 = __importStar(require("sqlite3"));
// إعداد قاعدة البيانات
let db;
function createWindow() {
    // إنشاء نافذة المتصفح
    const mainWindow = new electron_1.BrowserWindow({
        height: 800,
        width: 1200,
        minHeight: 600,
        minWidth: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
            sandbox: false,
        },
        icon: path.join(__dirname, '../assets/icon.png'),
        title: 'إدارة متجر قطع غيار الشاحنات - Truck Parts Manager',
        show: false, // لا تظهر النافذة حتى تكون جاهزة
    });
    // تحميل التطبيق
    if (process.env.NODE_ENV === 'development') {
        mainWindow.loadURL('http://localhost:5173');
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    }
    // إظهار النافذة عندما تكون جاهزة
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });
    // إعداد القائمة العربية
    createArabicMenu();
}
function createArabicMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        // إضافة منطق إنشاء عنصر جديد
                    }
                },
                {
                    label: 'فتح',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => {
                        // إضافة منطق فتح ملف
                    }
                },
                { type: 'separator' },
                {
                    label: 'نسخة احتياطية',
                    click: () => {
                        createBackup();
                    }
                },
                {
                    label: 'استعادة',
                    click: () => {
                        restoreBackup();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        electron_1.app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        electron_1.dialog.showMessageBox({
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'إدارة متجر قطع غيار الشاحنات',
                            detail: 'تطبيق شامل لإدارة المخزون والمبيعات والعملاء والموردين\nالإصدار 1.0.0'
                        });
                    }
                }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
}
// إعداد قاعدة البيانات
function initializeDatabase() {
    try {
        const dbPath = path.join(__dirname, '../database/truck_parts.db');
        // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        const dbDir = path.dirname(dbPath);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }
        // فتح قاعدة البيانات
        db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err);
                return;
            }
            console.log('Connected to SQLite database');
            // تفعيل Foreign Keys
            db.run('PRAGMA foreign_keys = ON');
            // قراءة وتنفيذ schema إذا كانت قاعدة البيانات فارغة
            db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
                if (err) {
                    console.error('Error checking tables:', err);
                    return;
                }
                if (tables.length === 0) {
                    const schemaPath = path.join(__dirname, '../database/database_schema_enhanced_sqlite.sql');
                    if (fs.existsSync(schemaPath)) {
                        const schema = fs.readFileSync(schemaPath, 'utf8');
                        db.exec(schema, (err) => {
                            if (err) {
                                console.error('Error creating schema:', err);
                            }
                            else {
                                console.log('Database schema created successfully');
                            }
                        });
                    }
                }
            });
        });
        console.log('Database initialized successfully');
    }
    catch (error) {
        console.error('Error initializing database:', error);
    }
}
// إنشاء نسخة احتياطية
async function createBackup() {
    try {
        const result = await electron_1.dialog.showSaveDialog({
            title: 'حفظ نسخة احتياطية',
            defaultPath: `backup_${new Date().toISOString().split('T')[0]}.db`,
            filters: [
                { name: 'قاعدة بيانات', extensions: ['db'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ]
        });
        if (!result.canceled && result.filePath) {
            const sourcePath = path.join(__dirname, '../database/truck_parts.db');
            fs.copyFileSync(sourcePath, result.filePath);
            electron_1.dialog.showMessageBox({
                type: 'info',
                title: 'نجح الحفظ',
                message: 'تم إنشاء النسخة الاحتياطية بنجاح'
            });
        }
    }
    catch (error) {
        electron_1.dialog.showErrorBox('خطأ', 'فشل في إنشاء النسخة الاحتياطية');
    }
}
// استعادة نسخة احتياطية
async function restoreBackup() {
    try {
        const result = await electron_1.dialog.showOpenDialog({
            title: 'اختيار نسخة احتياطية',
            filters: [
                { name: 'قاعدة بيانات', extensions: ['db'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });
        if (!result.canceled && result.filePaths.length > 0) {
            const targetPath = path.join(__dirname, '../database/truck_parts.db');
            // إغلاق قاعدة البيانات الحالية (مؤقتاً معطل)
            // if (db) {
            //   db.close();
            // }
            // نسخ الملف
            fs.copyFileSync(result.filePaths[0], targetPath);
            // إعادة فتح قاعدة البيانات (مؤقتاً معطل)
            // db = new Database(targetPath);
            electron_1.dialog.showMessageBox({
                type: 'info',
                title: 'نجحت الاستعادة',
                message: 'تم استعادة النسخة الاحتياطية بنجاح'
            });
        }
    }
    catch (error) {
        electron_1.dialog.showErrorBox('خطأ', 'فشل في استعادة النسخة الاحتياطية');
    }
}
// معالجات IPC لقاعدة البيانات
electron_1.ipcMain.handle('db-query', async (event, sql, params = []) => {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('Database not initialized'));
            return;
        }
        db.all(sql, params, (err, rows) => {
            if (err) {
                console.error('Database query error:', err);
                reject(err);
            }
            else {
                resolve(rows);
            }
        });
    });
});
electron_1.ipcMain.handle('db-run', async (event, sql, params = []) => {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('Database not initialized'));
            return;
        }
        db.run(sql, params, function (err) {
            if (err) {
                console.error('Database run error:', err);
                reject(err);
            }
            else {
                resolve({ lastID: this.lastID, changes: this.changes });
            }
        });
    });
});
// أحداث التطبيق
electron_1.app.whenReady().then(() => {
    initializeDatabase();
    createWindow();
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (db) {
            db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                }
                else {
                    console.log('Database closed');
                }
            });
        }
        electron_1.app.quit();
    }
});
electron_1.app.on('before-quit', () => {
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            }
            else {
                console.log('Database closed');
            }
        });
    }
    console.log('App is closing');
});
//# sourceMappingURL=main.js.map