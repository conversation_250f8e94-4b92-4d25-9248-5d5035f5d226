# -*- coding: utf-8 -*-
"""
مدير الاتصال بقاعدة البيانات
Database Connection Manager

يدير الاتصال بقاعدة البيانات والعمليات الأساسية
Manages database connection and basic operations
"""

import sqlite3
import logging
import threading
import shutil
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager

from config.database_config import DatabaseConfig

class DatabaseManager:
    """
    مدير قاعدة البيانات
    Database Manager Class
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """تهيئة مدير قاعدة البيانات - Initialize database manager"""
        self.db_path = db_path or self._get_default_db_path()
        self.connection = None
        self.lock = threading.RLock()
        self._local = threading.local()
        
        # إعداد السجلات
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def _get_default_db_path(self) -> str:
        """الحصول على المسار الافتراضي لقاعدة البيانات - Get default database path"""
        from config.settings import AppSettings
        settings = AppSettings()
        return settings.get("database", "path", "truck_parts.db")
    
    def _get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال قاعدة البيانات للخيط الحالي - Get database connection for current thread"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            self._local.connection = self._create_connection()
        return self._local.connection
    
    def _create_connection(self) -> sqlite3.Connection:
        """إنشاء اتصال جديد بقاعدة البيانات - Create new database connection"""
        try:
            # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            # Create database directory if it doesn't exist
            db_dir = Path(self.db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
            
            # إنشاء الاتصال
            # Create connection
            conn = sqlite3.connect(
                self.db_path,
                timeout=DatabaseConfig.CONNECTION_TIMEOUT,
                check_same_thread=False
            )
            
            # تعيين إعدادات الاتصال
            # Set connection settings
            conn.execute(f"PRAGMA busy_timeout = {DatabaseConfig.BUSY_TIMEOUT}")
            
            for pragma, value in DatabaseConfig.PRAGMA_SETTINGS.items():
                conn.execute(f"PRAGMA {pragma} = {value}")
            
            # تعيين row factory لإرجاع النتائج كقاموس
            # Set row factory to return results as dictionary
            conn.row_factory = sqlite3.Row
            
            self.logger.info(f"تم إنشاء اتصال قاعدة البيانات - Database connection created: {self.db_path}")
            return conn
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء اتصال قاعدة البيانات - Database connection error: {e}")
            raise
    
    def initialize(self) -> bool:
        """تهيئة قاعدة البيانات - Initialize database"""
        try:
            with self.lock:
                # التحقق من وجود قاعدة البيانات
                # Check if database exists
                db_exists = Path(self.db_path).exists()
                
                if not db_exists:
                    self.logger.info("إنشاء قاعدة بيانات جديدة - Creating new database")
                    self._create_database()
                else:
                    self.logger.info("قاعدة البيانات موجودة - Database exists")
                    # التحقق من سلامة قاعدة البيانات
                    # Check database integrity
                    if not self._check_database_integrity():
                        self.logger.warning("مشكلة في سلامة قاعدة البيانات - Database integrity issue")
                        return False
                
                # تحديث المخطط إذا لزم الأمر
                # Update schema if needed
                self._update_schema()
                
                self.logger.info("تم تهيئة قاعدة البيانات بنجاح - Database initialized successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات - Database initialization error: {e}")
            return False
    
    def _create_database(self):
        """إنشاء قاعدة البيانات - Create database"""
        try:
            # قراءة مخطط قاعدة البيانات من الملف
            # Read database schema from file
            schema_file = DatabaseConfig.get_schema_file_path()
            
            if schema_file.exists():
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # تنفيذ مخطط قاعدة البيانات
                # Execute database schema
                conn = self._get_connection()
                conn.executescript(schema_sql)
                conn.commit()
                
                self.logger.info("تم إنشاء مخطط قاعدة البيانات - Database schema created")
            else:
                # استخدام المخطط الأساسي
                # Use basic schema
                self._create_basic_schema()
            
            # إدراج البيانات الافتراضية
            # Insert default data
            self._insert_default_data()
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات - Database creation error: {e}")
            raise
    
    def _create_basic_schema(self):
        """إنشاء المخطط الأساسي - Create basic schema"""
        conn = self._get_connection()
        
        # تنفيذ استعلامات إنشاء الجداول
        # Execute table creation queries
        conn.executescript(DatabaseConfig.CREATE_TABLES_SQL)
        conn.executescript(DatabaseConfig.CREATE_INDEXES_SQL)
        conn.executescript(DatabaseConfig.CREATE_TRIGGERS_SQL)
        
        conn.commit()
        self.logger.info("تم إنشاء المخطط الأساسي - Basic schema created")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية - Insert default data"""
        try:
            conn = self._get_connection()
            conn.executescript(DatabaseConfig.INSERT_DEFAULT_DATA_SQL)
            conn.commit()
            self.logger.info("تم إدراج البيانات الافتراضية - Default data inserted")
        except Exception as e:
            self.logger.error(f"خطأ في إدراج البيانات الافتراضية - Default data insertion error: {e}")
    
    def _check_database_integrity(self) -> bool:
        """فحص سلامة قاعدة البيانات - Check database integrity"""
        try:
            conn = self._get_connection()
            result = conn.execute("PRAGMA integrity_check").fetchone()
            
            if result and result[0] == "ok":
                self.logger.info("فحص سلامة قاعدة البيانات: ناجح - Database integrity check: OK")
                return True
            else:
                self.logger.error(f"فحص سلامة قاعدة البيانات: فشل - Database integrity check failed: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في فحص سلامة قاعدة البيانات - Database integrity check error: {e}")
            return False
    
    def _update_schema(self):
        """تحديث مخطط قاعدة البيانات - Update database schema"""
        # يمكن إضافة منطق تحديث المخطط هنا
        # Schema update logic can be added here
        pass
    
    @contextmanager
    def get_cursor(self):
        """الحصول على مؤشر قاعدة البيانات - Get database cursor"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    @contextmanager
    def transaction(self):
        """إدارة المعاملات - Transaction management"""
        conn = self._get_connection()
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """تنفيذ استعلام وإرجاع النتائج - Execute query and return results"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام - Query execution error: {e}")
            raise
    
    def execute_non_query(self, query: str, params: Optional[Tuple] = None) -> int:
        """تنفيذ استعلام بدون إرجاع نتائج - Execute non-query and return affected rows"""
        try:
            with self.transaction() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.rowcount
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام - Non-query execution error: {e}")
            raise
    
    def execute_scalar(self, query: str, params: Optional[Tuple] = None) -> Any:
        """تنفيذ استعلام وإرجاع قيمة واحدة - Execute query and return single value"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام - Scalar query execution error: {e}")
            raise
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات - Create database backup"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = DatabaseConfig.get_backup_filename(timestamp)
                backup_path = Path(self.db_path).parent / "backups" / backup_filename
            
            # إنشاء مجلد النسخ الاحتياطية
            # Create backup directory
            backup_dir = Path(backup_path).parent
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # نسخ قاعدة البيانات
            # Copy database
            shutil.copy2(self.db_path, backup_path)
            
            self.logger.info(f"تم إنشاء نسخة احتياطية - Backup created: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية - Backup creation error: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية - Restore database from backup"""
        try:
            if not Path(backup_path).exists():
                self.logger.error(f"ملف النسخة الاحتياطية غير موجود - Backup file not found: {backup_path}")
                return False
            
            # إغلاق الاتصالات الحالية
            # Close current connections
            self.close_connections()
            
            # نسخ النسخة الاحتياطية
            # Copy backup file
            shutil.copy2(backup_path, self.db_path)
            
            # إعادة تهيئة الاتصال
            # Reinitialize connection
            self.initialize()
            
            self.logger.info(f"تم استعادة قاعدة البيانات - Database restored from: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في استعادة قاعدة البيانات - Database restoration error: {e}")
            return False
    
    def close_connections(self):
        """إغلاق جميع الاتصالات - Close all connections"""
        try:
            if hasattr(self._local, 'connection') and self._local.connection:
                self._local.connection.close()
                self._local.connection = None
            
            if self.connection:
                self.connection.close()
                self.connection = None
                
            self.logger.info("تم إغلاق اتصالات قاعدة البيانات - Database connections closed")
            
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الاتصالات - Connection closing error: {e}")
    
    def __del__(self):
        """تنظيف الموارد - Cleanup resources"""
        self.close_connections()
