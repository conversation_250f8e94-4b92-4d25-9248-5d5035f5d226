# تطبيق إدارة متجر قطع غيار الشاحنات
## Truck Parts Store Management Application

تطبيق سطح مكتب هجين شامل لإدارة متجر قطع غيار الشاحنات، مبني باستخدام Electron و React مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏪 إدارة المخزون
- إضافة وتعديل وحذف قطع الغيار
- تتبع الكميات والمواقع في المخزن
- تصنيف القطع حسب الفئات والماركات
- تنبيهات الحد الأدنى للمخزون
- إدارة الباركود والأرقام التسلسلية

### 💰 إدارة المبيعات
- إنشاء فواتير البيع
- تتبع حالة الدفع
- طباعة الفواتير والإيصالات
- إدارة الخصومات والضرائب
- تتبع قنوات البيع المختلفة

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المشتريات
- إدارة حسابات العملاء
- نظام نقاط الولاء
- سجل التواصل مع العملاء

### 🚚 إدارة الموردين
- قاعدة بيانات الموردين
- إدارة طلبات الشراء
- تتبع فواتير الموردين
- تقييم أداء الموردين
- إدارة شروط الدفع

### 📊 التقارير والتحليلات
- تقارير المبيعات والإيرادات
- تقارير المخزون والحركة
- تحليل أداء العملاء
- تقييم الموردين
- الملخصات المالية

### 💳 إدارة الديون
- تتبع الديون المستحقة
- إدارة دفعات العملاء
- تذكيرات الدفع التلقائية
- تقارير الديون والمتأخرات

## التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **Desktop Framework**: Electron
- **UI Library**: Material-UI (MUI)
- **Database**: SQLite
- **Internationalization**: i18next
- **Build Tool**: Vite
- **Styling**: CSS-in-JS with MUI

## متطلبات النظام

- Node.js 18 أو أحدث
- npm أو yarn
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd truck-parts-manager
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### 4. بناء التطبيق للإنتاج
```bash
npm run build
```

### 5. إنشاء ملفات التثبيت
```bash
# لنظام Windows
npm run package:win

# لنظام macOS
npm run package:mac

# لنظام Linux
npm run package:linux
```

## هيكل المشروع

```
truck-parts-manager/
├── src/
│   ├── components/          # المكونات المشتركة
│   │   └── Layout/         # مكونات التخطيط
│   ├── pages/              # صفحات التطبيق
│   ├── i18n/               # ملفات الترجمة
│   ├── types/              # تعريفات TypeScript
│   ├── utils/              # الوظائف المساعدة
│   ├── main.tsx            # نقطة دخول React
│   ├── main.ts             # العملية الرئيسية لـ Electron
│   └── preload.ts          # سكريبت preload
├── database/               # ملفات قاعدة البيانات
├── assets/                 # الصور والملفات الثابتة
├── dist/                   # ملفات البناء
└── release/                # ملفات التوزيع
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite مع الجداول التالية:

- **brands**: ماركات الشاحنات
- **models**: موديلات الشاحنات
- **categories**: فئات قطع الغيار
- **parts**: قطع الغيار
- **suppliers**: الموردين
- **customers**: العملاء
- **sales_invoices**: فواتير المبيعات
- **purchase_invoices**: فواتير المشتريات
- **inventory_transactions**: حركات المخزون
- **debts**: إدارة الديون
- وجداول أخرى للدعم

## الميزات المتقدمة

### النسخ الاحتياطي والاستعادة
- نسخ احتياطي تلقائي ويدوي
- استعادة البيانات من النسخ الاحتياطية
- تشفير النسخ الاحتياطية (اختياري)

### دعم متعدد اللغات
- العربية (افتراضي)
- الإنجليزية
- إمكانية إضافة لغات أخرى

### الأمان
- تشفير كلمات المرور
- إدارة صلاحيات المستخدمين
- سجل العمليات والتدقيق

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## خارطة الطريق

### الإصدار 1.1
- [ ] تطبيق الهاتف المحمول
- [ ] مزامنة السحابة
- [ ] تقارير متقدمة مع الرسوم البيانية

### الإصدار 1.2
- [ ] نظام إدارة المستودعات المتعددة
- [ ] تكامل مع أنظمة المحاسبة
- [ ] API للتكامل مع التطبيقات الأخرى

---

**تم تطوير هذا التطبيق خصيصاً لمتاجر قطع غيار الشاحنات في المنطقة العربية**
