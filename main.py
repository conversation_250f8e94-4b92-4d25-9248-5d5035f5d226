#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة قطع غيار الشاحنات
Truck Spare Parts Management System

نقطة دخول التطبيق الرئيسية
Main application entry point

المطور: نظام إدارة قطع الغيار
Developer: Spare Parts Management System
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# PyQt5 imports
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt5.QtGui import QPixmap, QFont, QIcon

# Local imports
from config.settings import AppSettings
from database.connection import DatabaseManager
from ui.main_window import MainWindow
from utils.arabic_utils import setup_arabic_support
from utils.helpers import setup_logging, check_system_requirements

class TruckPartsApp(QApplication):
    """
    التطبيق الرئيسي لنظام إدارة قطع غيار الشاحنات
    Main application class for Truck Parts Management System
    """
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # إعداد الخصائص الأساسية للتطبيق
        # Setup basic application properties
        self.setApplicationName("نظام إدارة قطع غيار الشاحنات")
        self.setApplicationDisplayName("Truck Parts Management")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("Spare Parts Solutions")
        self.setOrganizationDomain("spareparts.local")
        
        # إعداد الأيقونة
        # Setup application icon
        self.setup_app_icon()
        
        # إعداد دعم اللغة العربية
        # Setup Arabic language support
        setup_arabic_support()
        
        # إعداد الخط العربي
        # Setup Arabic font
        self.setup_arabic_font()
        
        # متغيرات التطبيق
        # Application variables
        self.settings = None
        self.db_manager = None
        self.main_window = None
        self.splash = None
        
    def setup_app_icon(self):
        """إعداد أيقونة التطبيق - Setup application icon"""
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
    
    def setup_arabic_font(self):
        """إعداد الخط العربي - Setup Arabic font"""
        # تحديد خط عربي مناسب
        # Set appropriate Arabic font
        arabic_fonts = [
            "Segoe UI", "Tahoma", "Arial Unicode MS", 
            "DejaVu Sans", "Liberation Sans"
        ]
        
        for font_name in arabic_fonts:
            font = QFont(font_name, 10)
            if font.exactMatch():
                self.setFont(font)
                break
    
    def show_splash_screen(self):
        """عرض شاشة البداية - Show splash screen"""
        splash_path = project_root / "resources" / "images" / "splash.png"
        
        if splash_path.exists():
            pixmap = QPixmap(str(splash_path))
        else:
            # إنشاء شاشة بداية افتراضية
            # Create default splash screen
            pixmap = QPixmap(400, 300)
            pixmap.fill(Qt.white)
        
        self.splash = QSplashScreen(pixmap)
        self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        self.splash.show()
        
        # عرض رسالة التحميل
        # Show loading message
        self.splash.showMessage(
            "جاري تحميل نظام إدارة قطع غيار الشاحنات...\nLoading Truck Parts Management System...",
            Qt.AlignBottom | Qt.AlignCenter,
            Qt.black
        )
        
        self.processEvents()
    
    def initialize_application(self):
        """تهيئة التطبيق - Initialize application"""
        try:
            # إعداد نظام السجلات
            # Setup logging system
            setup_logging()
            logging.info("بدء تشغيل التطبيق - Starting application")
            
            # فحص متطلبات النظام
            # Check system requirements
            if not check_system_requirements():
                self.show_error_message(
                    "خطأ في متطلبات النظام",
                    "النظام لا يلبي الحد الأدنى من المتطلبات المطلوبة"
                )
                return False
            
            # تحميل الإعدادات
            # Load settings
            self.settings = AppSettings()
            logging.info("تم تحميل الإعدادات - Settings loaded")
            
            # تهيئة قاعدة البيانات
            # Initialize database
            self.db_manager = DatabaseManager()
            if not self.db_manager.initialize():
                self.show_error_message(
                    "خطأ في قاعدة البيانات",
                    "فشل في الاتصال بقاعدة البيانات"
                )
                return False
            
            logging.info("تم تهيئة قاعدة البيانات - Database initialized")
            
            return True
            
        except Exception as e:
            logging.error(f"خطأ في تهيئة التطبيق - Application initialization error: {e}")
            self.show_error_message(
                "خطأ في التطبيق",
                f"حدث خطأ أثناء تهيئة التطبيق:\n{str(e)}"
            )
            return False
    
    def show_main_window(self):
        """عرض النافذة الرئيسية - Show main window"""
        try:
            self.main_window = MainWindow(self.db_manager, self.settings)
            self.main_window.show()
            
            # إخفاء شاشة البداية
            # Hide splash screen
            if self.splash:
                self.splash.finish(self.main_window)
            
            logging.info("تم عرض النافذة الرئيسية - Main window displayed")
            
        except Exception as e:
            logging.error(f"خطأ في عرض النافذة الرئيسية - Main window display error: {e}")
            self.show_error_message(
                "خطأ في النافذة الرئيسية",
                f"فشل في عرض النافذة الرئيسية:\n{str(e)}"
            )
    
    def show_error_message(self, title, message):
        """عرض رسالة خطأ - Show error message"""
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def run(self):
        """تشغيل التطبيق - Run application"""
        # عرض شاشة البداية
        # Show splash screen
        self.show_splash_screen()
        
        # تأخير لعرض شاشة البداية
        # Delay to show splash screen
        QTimer.singleShot(1000, self._delayed_initialization)
        
        # تشغيل حلقة الأحداث
        # Start event loop
        return self.exec_()
    
    def _delayed_initialization(self):
        """تهيئة مؤجلة للتطبيق - Delayed application initialization"""
        if self.initialize_application():
            QTimer.singleShot(500, self.show_main_window)
        else:
            self.quit()

def main():
    """الدالة الرئيسية - Main function"""
    # إنشاء التطبيق
    # Create application
    app = TruckPartsApp(sys.argv)
    
    # تشغيل التطبيق
    # Run application
    exit_code = app.run()
    
    # تنظيف الموارد
    # Cleanup resources
    logging.info("إنهاء التطبيق - Application shutdown")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
