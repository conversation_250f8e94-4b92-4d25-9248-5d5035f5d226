import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Paper,
  Divider,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  ShoppingCart as CartIcon,
  Business as SupplierIcon,
  Schedule as ScheduleIcon,
  Inventory as InventoryIcon,
  LocalShipping as ShippingIcon,
} from '@mui/icons-material';

// تعريف أنواع البيانات
interface Part {
  id: number;
  part_number: string;
  part_name: string;
  category: string;
  quantity: number;
  min_quantity: number;
  shelf_location: string;
  last_sale_date?: string;
  last_purchase_date?: string;
  preferred_supplier: string;
  purchase_price: number;
  selling_price: number;
  is_active: boolean;
}

interface LowStockTabProps {
  lowStockParts: Part[];
  onCreatePurchaseOrder: (part: Part) => void;
}

const LowStockTab: React.FC<LowStockTabProps> = ({ lowStockParts, onCreatePurchaseOrder }) => {
  // تصنيف القطع حسب الحالة
  const outOfStockParts = lowStockParts.filter(part => part.quantity === 0);
  const criticalStockParts = lowStockParts.filter(part => part.quantity > 0 && part.quantity <= part.min_quantity / 2);
  const lowStockOnlyParts = lowStockParts.filter(part => part.quantity > part.min_quantity / 2 && part.quantity <= part.min_quantity);

  const getStockStatus = (part: Part) => {
    if (part.quantity === 0) {
      return { label: 'نفد المخزون', color: 'error' as const, icon: <ErrorIcon /> };
    } else if (part.quantity <= part.min_quantity / 2) {
      return { label: 'حرج', color: 'error' as const, icon: <WarningIcon /> };
    } else {
      return { label: 'منخفض', color: 'warning' as const, icon: <WarningIcon /> };
    }
  };

  const getDaysSinceLastPurchase = (lastPurchaseDate?: string) => {
    if (!lastPurchaseDate) return null;
    const today = new Date();
    const lastPurchase = new Date(lastPurchaseDate);
    const diffTime = Math.abs(today.getTime() - lastPurchase.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const renderPartCard = (part: Part) => {
    const status = getStockStatus(part);
    const daysSinceLastPurchase = getDaysSinceLastPurchase(part.last_purchase_date);
    const isOldStock = daysSinceLastPurchase && daysSinceLastPurchase > 30;

    return (
      <Card 
        key={part.id} 
        sx={{ 
          mb: 2,
          border: part.quantity === 0 ? '2px solid #ef4444' : '1px solid #e2e8f0',
          backgroundColor: part.quantity === 0 ? 'rgba(239, 68, 68, 0.05)' : 'background.paper'
        }}
      >
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InventoryIcon sx={{ color: '#64748b' }} />
                <Box>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                    {part.part_name}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {part.part_number} • {part.category}
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    الموقع: {part.shelf_location}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={2}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" sx={{ 
                  color: part.quantity === 0 ? '#ef4444' : '#f59e0b',
                  fontWeight: 'bold'
                }}>
                  {part.quantity}
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  من {part.min_quantity} (الحد الأدنى)
                </Typography>
                <Box sx={{ mt: 1 }}>
                  <Chip
                    icon={status.icon}
                    label={status.label}
                    size="small"
                    color={status.color}
                  />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <SupplierIcon sx={{ fontSize: 16, color: '#64748b' }} />
                  <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    {part.preferred_supplier}
                  </Typography>
                </Box>
                
                {part.last_sale_date && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <ScheduleIcon sx={{ fontSize: 16, color: '#64748b' }} />
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      آخر بيع: {new Date(part.last_sale_date).toLocaleDateString('ar-DZ')}
                    </Typography>
                  </Box>
                )}
                
                {part.last_purchase_date && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ShippingIcon sx={{ fontSize: 16, color: '#64748b' }} />
                    <Typography variant="caption" sx={{ 
                      color: isOldStock ? '#ef4444' : 'text.secondary'
                    }}>
                      آخر شراء: {new Date(part.last_purchase_date).toLocaleDateString('ar-DZ')}
                      {isOldStock && ` (منذ ${daysSinceLastPurchase} يوم)`}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  سعر الشراء: {part.purchase_price.toLocaleString()} دج
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<CartIcon />}
                  onClick={() => onCreatePurchaseOrder(part)}
                  sx={{ 
                    fontFamily: 'Cairo, sans-serif',
                    backgroundColor: part.quantity === 0 ? '#ef4444' : '#3b82f6'
                  }}
                >
                  إنشاء طلبية شراء
                </Button>
              </Box>
            </Grid>
          </Grid>

          {isOldStock && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="caption" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                تحذير: لم يتم تجديد المخزون لأكثر من 30 يوم
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  };

  if (lowStockParts.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <InventoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary', mb: 1 }}>
          ممتاز! جميع القطع في المخزون
        </Typography>
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
          لا توجد قطع وصلت للحد الأدنى من المخزون
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center', border: '2px solid #ef4444' }}>
            <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
              {outOfStockParts.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              نفد المخزون
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center', border: '2px solid #f59e0b' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {criticalStockParts.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              مخزون حرج
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center', border: '2px solid #f59e0b' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {lowStockOnlyParts.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              مخزون منخفض
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* تنبيه عام */}
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          يتطلب اتخاذ إجراء فوري!
        </Typography>
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          يوجد {outOfStockParts.length} قطع نفدت تماماً و {criticalStockParts.length} قطع في حالة حرجة.
          يُنصح بإنشاء طلبيات شراء عاجلة لتجنب توقف المبيعات.
        </Typography>
      </Alert>

      {/* القطع التي نفدت */}
      {outOfStockParts.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif', color: '#ef4444', fontWeight: 'bold' }}>
            قطع نفد مخزونها ({outOfStockParts.length})
          </Typography>
          {outOfStockParts.map(renderPartCard)}
        </Box>
      )}

      {/* القطع في حالة حرجة */}
      {criticalStockParts.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif', color: '#f59e0b', fontWeight: 'bold' }}>
            قطع في حالة حرجة ({criticalStockParts.length})
          </Typography>
          {criticalStockParts.map(renderPartCard)}
        </Box>
      )}

      {/* القطع منخفضة المخزون */}
      {lowStockOnlyParts.length > 0 && (
        <Box>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif', color: '#f59e0b', fontWeight: 'bold' }}>
            قطع منخفضة المخزون ({lowStockOnlyParts.length})
          </Typography>
          {lowStockOnlyParts.map(renderPartCard)}
        </Box>
      )}

      {/* زر إنشاء طلبيات شراء جماعية */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<CartIcon />}
          onClick={() => {
            // سيتم تنفيذ إنشاء طلبيات شراء جماعية
            lowStockParts.forEach(part => onCreatePurchaseOrder(part));
          }}
          sx={{ 
            fontFamily: 'Cairo, sans-serif',
            backgroundColor: '#ef4444',
            '&:hover': {
              backgroundColor: '#dc2626',
            }
          }}
        >
          إنشاء طلبيات شراء لجميع القطع ({lowStockParts.length})
        </Button>
      </Box>
    </Box>
  );
};

export default LowStockTab;
