import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Divider,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Sms as SmsIcon,
  WhatsApp as WhatsAppIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { useNotifications } from '../../contexts/NotificationContext';

// تعريف أنواع البيانات
interface DebtReminder {
  id: number;
  customer_id: number;
  customer_name: string;
  debt_id: number;
  invoice_number: string;
  reminder_type: 'before_due' | 'on_due' | 'after_due';
  reminder_method: 'notification' | 'sms' | 'whatsapp' | 'phone' | 'email';
  scheduled_date: string;
  status: 'pending' | 'sent' | 'failed';
  message: string;
  created_at: string;
  sent_at?: string;
}

interface FollowUp {
  id: number;
  customer_id: number;
  customer_name: string;
  contact_date: string;
  contact_method: 'phone' | 'sms' | 'whatsapp' | 'email' | 'visit';
  contact_person: string;
  response: string;
  payment_promise?: string;
  next_follow_up?: string;
  notes: string;
  created_at: string;
}

interface DebtReminderSystemProps {
  customerId?: number;
  customerName?: string;
}

const DebtReminderSystem: React.FC<DebtReminderSystemProps> = ({ customerId, customerName }) => {
  const { addNotification } = useNotifications();
  const [reminders, setReminders] = useState<DebtReminder[]>([]);
  const [followUps, setFollowUps] = useState<FollowUp[]>([]);
  const [reminderDialogOpen, setReminderDialogOpen] = useState(false);
  const [followUpDialogOpen, setFollowUpDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);

  useEffect(() => {
    loadReminders();
    loadFollowUps();
  }, [customerId]);

  const loadReminders = async () => {
    // محاكاة تحميل التذكيرات
    const mockReminders: DebtReminder[] = [
      {
        id: 1,
        customer_id: customerId || 1,
        customer_name: customerName || 'شركة النقل السريع',
        debt_id: 1,
        invoice_number: 'INV-2024-001',
        reminder_type: 'before_due',
        reminder_method: 'sms',
        scheduled_date: '2024-06-12',
        status: 'sent',
        message: 'تذكير: يستحق دفع فاتورة INV-2024-001 بتاريخ 15/06/2024',
        created_at: '2024-06-10',
        sent_at: '2024-06-12',
      },
      {
        id: 2,
        customer_id: customerId || 1,
        customer_name: customerName || 'شركة النقل السريع',
        debt_id: 1,
        invoice_number: 'INV-2024-001',
        reminder_type: 'on_due',
        reminder_method: 'whatsapp',
        scheduled_date: '2024-06-15',
        status: 'pending',
        message: 'تذكير: يستحق دفع فاتورة INV-2024-001 اليوم',
        created_at: '2024-06-10',
      },
    ];

    setReminders(mockReminders);
  };

  const loadFollowUps = async () => {
    // محاكاة تحميل المتابعات
    const mockFollowUps: FollowUp[] = [
      {
        id: 1,
        customer_id: customerId || 1,
        customer_name: customerName || 'شركة النقل السريع',
        contact_date: '2024-06-10',
        contact_method: 'phone',
        contact_person: 'أحمد المدير المالي',
        response: 'وعد بالدفع خلال أسبوع',
        payment_promise: '2024-06-17',
        next_follow_up: '2024-06-18',
        notes: 'العميل يواجه صعوبات مالية مؤقتة',
        created_at: '2024-06-10',
      },
      {
        id: 2,
        customer_id: customerId || 1,
        customer_name: customerName || 'شركة النقل السريع',
        contact_date: '2024-06-05',
        contact_method: 'email',
        contact_person: 'مدير الحسابات',
        response: 'تم استلام الرسالة وسيتم الرد قريباً',
        notes: 'تم إرسال كشف حساب مفصل',
        created_at: '2024-06-05',
      },
    ];

    setFollowUps(mockFollowUps);
  };

  const handleSendReminder = async (reminder: DebtReminder) => {
    try {
      // محاكاة إرسال التذكير
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReminders(prev => prev.map(r => 
        r.id === reminder.id 
          ? { ...r, status: 'sent' as const, sent_at: new Date().toISOString() }
          : r
      ));

      addNotification({
        type: 'success',
        title: 'تم إرسال التذكير',
        message: `تم إرسال تذكير إلى ${reminder.customer_name} بنجاح`,
        priority: 'medium',
      });
    } catch (error) {
      setReminders(prev => prev.map(r => 
        r.id === reminder.id 
          ? { ...r, status: 'failed' as const }
          : r
      ));

      addNotification({
        type: 'error',
        title: 'فشل في إرسال التذكير',
        message: `فشل في إرسال التذكير إلى ${reminder.customer_name}`,
        priority: 'high',
      });
    }
  };

  const getReminderTypeLabel = (type: string) => {
    switch (type) {
      case 'before_due':
        return 'قبل الاستحقاق';
      case 'on_due':
        return 'يوم الاستحقاق';
      case 'after_due':
        return 'بعد الاستحقاق';
      default:
        return type;
    }
  };

  const getReminderMethodIcon = (method: string) => {
    switch (method) {
      case 'notification':
        return <NotificationsIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'whatsapp':
        return <WhatsAppIcon />;
      case 'phone':
        return <PhoneIcon />;
      case 'email':
        return <EmailIcon />;
      default:
        return <NotificationsIcon />;
    }
  };

  const getReminderMethodLabel = (method: string) => {
    switch (method) {
      case 'notification':
        return 'تنبيه';
      case 'sms':
        return 'رسالة نصية';
      case 'whatsapp':
        return 'واتساب';
      case 'phone':
        return 'مكالمة';
      case 'email':
        return 'بريد إلكتروني';
      default:
        return method;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'sent':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'sent':
        return 'تم الإرسال';
      case 'failed':
        return 'فشل';
      default:
        return status;
    }
  };

  const getContactMethodIcon = (method: string) => {
    switch (method) {
      case 'phone':
        return <PhoneIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'whatsapp':
        return <WhatsAppIcon />;
      case 'email':
        return <EmailIcon />;
      case 'visit':
        return <CheckIcon />;
      default:
        return <PhoneIcon />;
    }
  };

  const getContactMethodLabel = (method: string) => {
    switch (method) {
      case 'phone':
        return 'مكالمة هاتفية';
      case 'sms':
        return 'رسالة نصية';
      case 'whatsapp':
        return 'واتساب';
      case 'email':
        return 'بريد إلكتروني';
      case 'visit':
        return 'زيارة شخصية';
      default:
        return method;
    }
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        نظام التذكيرات والمتابعة
      </Typography>

      <Grid container spacing={3}>
        {/* التذكيرات التلقائية */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                  التذكيرات التلقائية
                </Typography>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setReminderDialogOpen(true)}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  إضافة تذكير
                </Button>
              </Box>

              <List>
                {reminders.map((reminder, index) => (
                  <React.Fragment key={reminder.id}>
                    <ListItem>
                      <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                        {getReminderMethodIcon(reminder.reminder_method)}
                      </Box>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                              {reminder.invoice_number}
                            </Typography>
                            <Chip
                              label={getReminderTypeLabel(reminder.reminder_type)}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                              {getReminderMethodLabel(reminder.reminder_method)} - {new Date(reminder.scheduled_date).toLocaleDateString('ar-DZ')}
                            </Typography>
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                              {reminder.message}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={getStatusLabel(reminder.status)}
                            size="small"
                            color={getStatusColor(reminder.status) as any}
                          />
                          {reminder.status === 'pending' && (
                            <Tooltip title="إرسال الآن">
                              <IconButton
                                size="small"
                                onClick={() => handleSendReminder(reminder)}
                                sx={{ color: '#10b981' }}
                              >
                                <NotificationsIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < reminders.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>

              {reminders.length === 0 && (
                <Typography sx={{ textAlign: 'center', color: 'text.secondary', py: 4 }}>
                  لا توجد تذكيرات مجدولة
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* سجل المتابعات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                  سجل المتابعات
                </Typography>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setFollowUpDialogOpen(true)}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  إضافة متابعة
                </Button>
              </Box>

              <List>
                {followUps.map((followUp, index) => (
                  <React.Fragment key={followUp.id}>
                    <ListItem>
                      <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                        {getContactMethodIcon(followUp.contact_method)}
                      </Box>
                      <ListItemText
                        primary={
                          <Box>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                              {getContactMethodLabel(followUp.contact_method)}
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                              {new Date(followUp.contact_date).toLocaleDateString('ar-DZ')} - {followUp.contact_person}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                              الرد: {followUp.response}
                            </Typography>
                            {followUp.payment_promise && (
                              <Typography variant="caption" sx={{ color: '#f59e0b' }}>
                                وعد بالدفع: {new Date(followUp.payment_promise).toLocaleDateString('ar-DZ')}
                              </Typography>
                            )}
                            {followUp.next_follow_up && (
                              <Typography variant="caption" sx={{ color: '#3b82f6', display: 'block' }}>
                                المتابعة التالية: {new Date(followUp.next_follow_up).toLocaleDateString('ar-DZ')}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton size="small" sx={{ color: '#3b82f6' }}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < followUps.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>

              {followUps.length === 0 && (
                <Typography sx={{ textAlign: 'center', color: 'text.secondary', py: 4 }}>
                  لا توجد متابعات مسجلة
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {reminders.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي التذكيرات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {reminders.filter(r => r.status === 'pending').length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              في الانتظار
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {reminders.filter(r => r.status === 'sent').length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              تم الإرسال
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#8b5cf6', fontWeight: 'bold' }}>
              {followUps.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              المتابعات
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DebtReminderSystem;
