import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Divider,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Payment as PaymentIcon,
  History as HistoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  AccountBalance as DebtIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';

// تعريف أنواع البيانات
interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  customer_type: string;
  total_debts: number;
  debt_status: string;
  overdue_amount: number;
  last_payment_date?: string;
}

interface CustomerDebt {
  id: number;
  customer_id: number;
  invoice_number: string;
  debt_amount: number;
  paid_amount: number;
  remaining_amount: number;
  due_date: string;
  status: 'pending' | 'overdue' | 'partially_paid' | 'fully_paid';
  created_date: string;
  last_payment_date?: string;
  source: string;
  notes: string;
}

interface DebtPayment {
  id: number;
  debt_id: number;
  amount: number;
  payment_date: string;
  payment_method: 'cash' | 'bank_transfer' | 'check';
  reference_number?: string;
  notes: string;
}

interface DebtTrend {
  date: string;
  total_debt: number;
  paid_amount: number;
  remaining_amount: number;
}

interface CustomerDebtManagementProps {
  open: boolean;
  onClose: () => void;
  customer: Customer | null;
}

const CustomerDebtManagement: React.FC<CustomerDebtManagementProps> = ({ open, onClose, customer }) => {
  const [customerDebts, setCustomerDebts] = useState<CustomerDebt[]>([]);
  const [debtPayments, setDebtPayments] = useState<DebtPayment[]>([]);
  const [debtTrends, setDebtTrends] = useState<DebtTrend[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);

  useEffect(() => {
    if (customer && open) {
      loadCustomerDebts();
    }
  }, [customer, open]);

  const loadCustomerDebts = async () => {
    if (!customer) return;

    setLoading(true);
    try {
      // محاكاة تحميل ديون العميل
      const mockDebts: CustomerDebt[] = [
        {
          id: 1,
          customer_id: customer.id,
          invoice_number: 'INV-2024-001',
          debt_amount: 150000,
          paid_amount: 50000,
          remaining_amount: 100000,
          due_date: '2024-06-15',
          status: 'partially_paid',
          created_date: '2024-05-01',
          last_payment_date: '2024-05-20',
          source: 'بيع قطع غيار',
          notes: 'دين من فاتورة قطع غيار شاحنة مرسيدس',
        },
        {
          id: 2,
          customer_id: customer.id,
          invoice_number: 'INV-2024-015',
          debt_amount: 75000,
          paid_amount: 0,
          remaining_amount: 75000,
          due_date: '2024-05-25',
          status: 'overdue',
          created_date: '2024-04-25',
          source: 'خدمات صيانة',
          notes: 'دين متأخر - يحتاج متابعة',
        },
        {
          id: 3,
          customer_id: customer.id,
          invoice_number: 'INV-2024-008',
          debt_amount: 25000,
          paid_amount: 25000,
          remaining_amount: 0,
          due_date: '2024-05-30',
          status: 'fully_paid',
          created_date: '2024-04-30',
          last_payment_date: '2024-05-28',
          source: 'بيع قطع غيار',
          notes: 'تم السداد كاملاً',
        },
      ];

      const mockPayments: DebtPayment[] = [
        {
          id: 1,
          debt_id: 1,
          amount: 50000,
          payment_date: '2024-05-20',
          payment_method: 'bank_transfer',
          reference_number: 'TRF-********-001',
          notes: 'دفعة جزئية',
        },
        {
          id: 2,
          debt_id: 3,
          amount: 25000,
          payment_date: '2024-05-28',
          payment_method: 'cash',
          notes: 'سداد كامل نقداً',
        },
      ];

      const mockTrends: DebtTrend[] = [
        { date: '2024-04-01', total_debt: 250000, paid_amount: 0, remaining_amount: 250000 },
        { date: '2024-04-15', total_debt: 250000, paid_amount: 0, remaining_amount: 250000 },
        { date: '2024-05-01', total_debt: 250000, paid_amount: 0, remaining_amount: 250000 },
        { date: '2024-05-15', total_debt: 250000, paid_amount: 25000, remaining_amount: 225000 },
        { date: '2024-05-20', total_debt: 250000, paid_amount: 75000, remaining_amount: 175000 },
        { date: '2024-05-28', total_debt: 250000, paid_amount: 100000, remaining_amount: 150000 },
        { date: '2024-06-01', total_debt: 250000, paid_amount: 100000, remaining_amount: 150000 },
      ];

      setCustomerDebts(mockDebts);
      setDebtPayments(mockPayments);
      setDebtTrends(mockTrends);
    } catch (error) {
      console.error('Error loading customer debts:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ScheduleIcon sx={{ color: '#f59e0b' }} />;
      case 'overdue':
        return <WarningIcon sx={{ color: '#ef4444' }} />;
      case 'partially_paid':
        return <PaymentIcon sx={{ color: '#3b82f6' }} />;
      case 'fully_paid':
        return <CheckIcon sx={{ color: '#10b981' }} />;
      default:
        return <ScheduleIcon sx={{ color: '#64748b' }} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'overdue':
        return 'متأخر';
      case 'partially_paid':
        return 'مدفوع جزئياً';
      case 'fully_paid':
        return 'مدفوع بالكامل';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'overdue':
        return 'error';
      case 'partially_paid':
        return 'info';
      case 'fully_paid':
        return 'success';
      default:
        return 'default';
    }
  };

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status !== 'fully_paid';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-DZ');
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash':
        return 'نقدي';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'check':
        return 'شيك';
      default:
        return method;
    }
  };

  // حساب الإحصائيات
  const totalDebts = customerDebts.reduce((sum, debt) => sum + debt.debt_amount, 0);
  const totalPaid = customerDebts.reduce((sum, debt) => sum + debt.paid_amount, 0);
  const totalRemaining = customerDebts.reduce((sum, debt) => sum + debt.remaining_amount, 0);
  const overdueDebts = customerDebts.filter(debt => isOverdue(debt.due_date, debt.status));
  const paymentProgress = totalDebts > 0 ? (totalPaid / totalDebts) * 100 : 0;

  if (!customer) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif', borderBottom: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <DebtIcon sx={{ color: '#f59e0b' }} />
          <Box>
            <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
              إدارة ديون العميل: {customer.name}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {customer.phone} • {customer.email}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* إحصائيات سريعة */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
                  {totalDebts.toLocaleString()}
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                  إجمالي الديون (دج)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
                  {totalPaid.toLocaleString()}
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                  المبلغ المدفوع (دج)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
                  {totalRemaining.toLocaleString()}
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                  المبلغ المتبقي (دج)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
                  {overdueDebts.length}
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                  ديون متأخرة
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* شريط التقدم */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                تقدم السداد
              </Typography>
              <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', color: 'primary.main' }}>
                {paymentProgress.toFixed(1)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={paymentProgress}
              sx={{ height: 10, borderRadius: 5 }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                مدفوع: {totalPaid.toLocaleString()} دج
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                متبقي: {totalRemaining.toLocaleString()} دج
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* تنبيهات الديون المتأخرة */}
        {overdueDebts.length > 0 && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
              تحذير: يوجد {overdueDebts.length} دين متأخر
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              إجمالي المبلغ المتأخر: {overdueDebts.reduce((sum, debt) => sum + debt.remaining_amount, 0).toLocaleString()} دج
            </Typography>
          </Alert>
        )}

        {/* الرسم البياني لتطور الديون */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              تطور الديون عبر الزمن
            </Typography>
            <Box sx={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={debtTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line
                    type="monotone"
                    dataKey="total_debt"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="إجمالي الديون"
                  />
                  <Line
                    type="monotone"
                    dataKey="paid_amount"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="المبلغ المدفوع"
                  />
                  <Line
                    type="monotone"
                    dataKey="remaining_amount"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="المبلغ المتبقي"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>

        {/* جدول تفاصيل الديون */}
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              تفاصيل الديون
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      رقم الفاتورة
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ الأصلي
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ المدفوع
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ المتبقي
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      تاريخ الاستحقاق
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      الحالة
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      العمليات
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {customerDebts.map((debt) => (
                    <TableRow
                      key={debt.id}
                      sx={{
                        backgroundColor: isOverdue(debt.due_date, debt.status) ? 'rgba(239, 68, 68, 0.1)' : 'transparent'
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <ReceiptIcon sx={{ color: '#64748b', fontSize: 20 }} />
                          <Box>
                            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                              {debt.invoice_number}
                            </Typography>
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                              {debt.source}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          {debt.debt_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#10b981' }}>
                          {debt.paid_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{
                          fontFamily: 'Cairo, sans-serif',
                          color: debt.remaining_amount > 0 ? '#ef4444' : '#10b981',
                          fontWeight: 'bold'
                        }}>
                          {debt.remaining_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{
                          fontFamily: 'Cairo, sans-serif',
                          color: isOverdue(debt.due_date, debt.status) ? '#ef4444' : 'text.primary'
                        }}>
                          {formatDate(debt.due_date)}
                        </Typography>
                        {isOverdue(debt.due_date, debt.status) && (
                          <Typography variant="caption" sx={{ color: '#ef4444', display: 'block' }}>
                            متأخر {Math.floor((new Date().getTime() - new Date(debt.due_date).getTime()) / (1000 * 60 * 60 * 24))} يوم
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(debt.status)}
                          label={getStatusLabel(debt.status)}
                          size="small"
                          color={getStatusColor(debt.status) as any}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="تسجيل دفعة">
                            <IconButton
                              size="small"
                              sx={{ color: '#10b981' }}
                              disabled={debt.status === 'fully_paid'}
                            >
                              <PaymentIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="تاريخ الدفعات">
                            <IconButton size="small" sx={{ color: '#3b82f6' }}>
                              <HistoryIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          إغلاق
        </Button>
        <Button variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          تسجيل دفعة جديدة
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerDebtManagement;
