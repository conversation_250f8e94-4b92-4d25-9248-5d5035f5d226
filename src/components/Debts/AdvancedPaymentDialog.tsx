import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  Chip,
  Divider,
  Alert,
  Paper,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  AttachFile as AttachFileIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

// تعريف أنواع البيانات
interface CustomerDebt {
  id: number;
  invoice_number: string;
  debt_amount: number;
  paid_amount: number;
  remaining_amount: number;
  due_date: string;
  status: string;
  source: string;
}

interface PaymentAllocation {
  debt_id: number;
  amount: number;
  is_selected: boolean;
}

interface PaymentData {
  total_amount: number;
  payment_date: string;
  payment_method: 'cash' | 'bank_transfer' | 'check';
  reference_number: string;
  notes: string;
  is_partial: boolean;
  allocations: PaymentAllocation[];
  receipt_image?: File;
}

interface AdvancedPaymentDialogProps {
  open: boolean;
  onClose: () => void;
  customerDebts: CustomerDebt[];
  customerName: string;
  onSave: (paymentData: PaymentData) => void;
}

const AdvancedPaymentDialog: React.FC<AdvancedPaymentDialogProps> = ({
  open,
  onClose,
  customerDebts,
  customerName,
  onSave,
}) => {
  const [paymentData, setPaymentData] = useState<PaymentData>({
    total_amount: 0,
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    reference_number: '',
    notes: '',
    is_partial: false,
    allocations: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showConfirmation, setShowConfirmation] = useState(false);

  // تحديث التخصيصات عند تغيير الديون
  useEffect(() => {
    if (customerDebts.length > 0) {
      const allocations = customerDebts
        .filter(debt => debt.remaining_amount > 0)
        .map(debt => ({
          debt_id: debt.id,
          amount: 0,
          is_selected: false,
        }));
      
      setPaymentData(prev => ({ ...prev, allocations }));
    }
  }, [customerDebts]);

  const handleInputChange = (field: keyof PaymentData) => (event: any) => {
    const value = event.target.value;
    setPaymentData(prev => ({ ...prev, [field]: value }));
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAllocationChange = (debtId: number, field: 'amount' | 'is_selected', value: number | boolean) => {
    setPaymentData(prev => ({
      ...prev,
      allocations: prev.allocations.map(allocation =>
        allocation.debt_id === debtId
          ? { ...allocation, [field]: value }
          : allocation
      ),
    }));
  };

  const handleAutoAllocate = () => {
    let remainingAmount = paymentData.total_amount;
    const updatedAllocations = [...paymentData.allocations];

    // ترتيب الديون حسب تاريخ الاستحقاق (الأقدم أولاً)
    const sortedDebts = customerDebts
      .filter(debt => debt.remaining_amount > 0)
      .sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());

    // توزيع المبلغ تلقائياً
    for (const debt of sortedDebts) {
      const allocationIndex = updatedAllocations.findIndex(a => a.debt_id === debt.id);
      if (allocationIndex !== -1 && remainingAmount > 0) {
        const allocatedAmount = Math.min(remainingAmount, debt.remaining_amount);
        updatedAllocations[allocationIndex] = {
          ...updatedAllocations[allocationIndex],
          amount: allocatedAmount,
          is_selected: allocatedAmount > 0,
        };
        remainingAmount -= allocatedAmount;
      }
    }

    setPaymentData(prev => ({ ...prev, allocations: updatedAllocations }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPaymentData(prev => ({ ...prev, receipt_image: file }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (paymentData.total_amount <= 0) {
      newErrors.total_amount = 'يجب إدخال مبلغ صحيح';
    }

    if (!paymentData.payment_date) {
      newErrors.payment_date = 'يجب تحديد تاريخ الدفعة';
    }

    if (paymentData.payment_method === 'bank_transfer' && !paymentData.reference_number) {
      newErrors.reference_number = 'يجب إدخال رقم التحويل';
    }

    if (paymentData.payment_method === 'check' && !paymentData.reference_number) {
      newErrors.reference_number = 'يجب إدخال رقم الشيك';
    }

    const totalAllocated = paymentData.allocations
      .filter(a => a.is_selected)
      .reduce((sum, a) => sum + a.amount, 0);

    if (totalAllocated > paymentData.total_amount) {
      newErrors.allocations = 'إجمالي التخصيصات يتجاوز مبلغ الدفعة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      setShowConfirmation(true);
    }
  };

  const handleConfirm = () => {
    onSave(paymentData);
    setShowConfirmation(false);
    onClose();
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash':
        return 'نقدي';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'check':
        return 'شيك';
      default:
        return method;
    }
  };

  const totalAllocated = paymentData.allocations
    .filter(a => a.is_selected)
    .reduce((sum, a) => sum + a.amount, 0);

  const remainingAmount = paymentData.total_amount - totalAllocated;

  const unpaidDebts = customerDebts.filter(debt => debt.remaining_amount > 0);

  return (
    <>
      <Dialog open={open && !showConfirmation} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif', borderBottom: '1px solid', borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <PaymentIcon sx={{ color: '#10b981' }} />
            <Box>
              <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                تسجيل دفعة جديدة
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                العميل: {customerName}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {/* معلومات الدفعة الأساسية */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                    معلومات الدفعة
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="مبلغ الدفعة"
                        type="number"
                        value={paymentData.total_amount}
                        onChange={handleInputChange('total_amount')}
                        error={!!errors.total_amount}
                        helperText={errors.total_amount}
                        InputProps={{
                          endAdornment: <Typography variant="body2">دج</Typography>,
                        }}
                        required
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="تاريخ الدفعة"
                        type="date"
                        value={paymentData.payment_date}
                        onChange={handleInputChange('payment_date')}
                        error={!!errors.payment_date}
                        helperText={errors.payment_date}
                        InputLabelProps={{ shrink: true }}
                        required
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <InputLabel>طريقة الدفع</InputLabel>
                        <Select
                          value={paymentData.payment_method}
                          onChange={handleInputChange('payment_method')}
                          label="طريقة الدفع"
                        >
                          <MenuItem value="cash">نقدي</MenuItem>
                          <MenuItem value="bank_transfer">تحويل بنكي</MenuItem>
                          <MenuItem value="check">شيك</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    {(paymentData.payment_method === 'bank_transfer' || paymentData.payment_method === 'check') && (
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label={paymentData.payment_method === 'bank_transfer' ? 'رقم التحويل' : 'رقم الشيك'}
                          value={paymentData.reference_number}
                          onChange={handleInputChange('reference_number')}
                          error={!!errors.reference_number}
                          helperText={errors.reference_number}
                          required
                        />
                      </Grid>
                    )}
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="ملاحظات"
                        multiline
                        rows={3}
                        value={paymentData.notes}
                        onChange={handleInputChange('notes')}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={paymentData.is_partial}
                            onChange={(e) => setPaymentData(prev => ({ ...prev, is_partial: e.target.checked }))}
                          />
                        }
                        label="دفعة جزئية"
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Button
                          variant="outlined"
                          component="label"
                          startIcon={<AttachFileIcon />}
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          إرفاق إيصال
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={handleFileUpload}
                          />
                        </Button>
                        {paymentData.receipt_image && (
                          <Chip
                            label={paymentData.receipt_image.name}
                            onDelete={() => setPaymentData(prev => ({ ...prev, receipt_image: undefined }))}
                            size="small"
                          />
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* تخصيص الدفعة للديون */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif' }}>
                      تخصيص الدفعة
                    </Typography>
                    <Button
                      size="small"
                      onClick={handleAutoAllocate}
                      disabled={paymentData.total_amount <= 0}
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      توزيع تلقائي
                    </Button>
                  </Box>
                  
                  {errors.allocations && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {errors.allocations}
                    </Alert>
                  )}
                  
                  <Paper sx={{ p: 2, mb: 2, backgroundColor: 'action.hover' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          المبلغ الإجمالي:
                        </Typography>
                        <Typography sx={{ color: '#3b82f6' }}>
                          {paymentData.total_amount.toLocaleString()} دج
                        </Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          المخصص:
                        </Typography>
                        <Typography sx={{ color: '#10b981' }}>
                          {totalAllocated.toLocaleString()} دج
                        </Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          المتبقي:
                        </Typography>
                        <Typography sx={{ color: remainingAmount >= 0 ? '#f59e0b' : '#ef4444' }}>
                          {remainingAmount.toLocaleString()} دج
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                  
                  <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                    {unpaidDebts.map((debt) => {
                      const allocation = paymentData.allocations.find(a => a.debt_id === debt.id);
                      if (!allocation) return null;
                      
                      return (
                        <React.Fragment key={debt.id}>
                          <ListItem>
                            <Box sx={{ width: '100%' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <Checkbox
                                  checked={allocation.is_selected}
                                  onChange={(e) => handleAllocationChange(debt.id, 'is_selected', e.target.checked)}
                                />
                                <Box sx={{ flexGrow: 1, ml: 1 }}>
                                  <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                                    {debt.invoice_number}
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                    متبقي: {debt.remaining_amount.toLocaleString()} دج
                                  </Typography>
                                </Box>
                              </Box>
                              
                              {allocation.is_selected && (
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="المبلغ المخصص"
                                  type="number"
                                  value={allocation.amount}
                                  onChange={(e) => handleAllocationChange(debt.id, 'amount', Number(e.target.value))}
                                  inputProps={{ min: 0, max: debt.remaining_amount }}
                                  InputProps={{
                                    endAdornment: <Typography variant="body2">دج</Typography>,
                                  }}
                                />
                              )}
                            </Box>
                          </ListItem>
                          <Divider />
                        </React.Fragment>
                      );
                    })}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
            إلغاء
          </Button>
          <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
            تسجيل الدفعة
          </Button>
        </DialogActions>
      </Dialog>

      {/* نافذة التأكيد */}
      <Dialog open={showConfirmation} onClose={() => setShowConfirmation(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
          تأكيد تسجيل الدفعة
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              يرجى مراجعة تفاصيل الدفعة قبل التأكيد
            </Typography>
          </Alert>
          
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              ملخص الدفعة
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                  المبلغ الإجمالي:
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {paymentData.total_amount.toLocaleString()} دج
                </Typography>
              </Grid>
              
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                  طريقة الدفع:
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {getPaymentMethodLabel(paymentData.payment_method)}
                </Typography>
              </Grid>
              
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                  تاريخ الدفعة:
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {new Date(paymentData.payment_date).toLocaleDateString('ar-DZ')}
                </Typography>
              </Grid>
              
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                  المبلغ المخصص:
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {totalAllocated.toLocaleString()} دج
                </Typography>
              </Grid>
            </Grid>
            
            {paymentData.reference_number && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                  رقم المرجع:
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {paymentData.reference_number}
                </Typography>
              </Box>
            )}
          </Paper>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setShowConfirmation(false)} sx={{ fontFamily: 'Cairo, sans-serif' }}>
            تراجع
          </Button>
          <Button onClick={handleConfirm} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
            تأكيد التسجيل
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AdvancedPaymentDialog;
