import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Print as PrintIcon,
  Download as DownloadIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  AccountBalance as DebtIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';

// تعريف أنواع البيانات
interface DebtReport {
  customer_name: string;
  total_debt: number;
  paid_amount: number;
  remaining_amount: number;
  overdue_amount: number;
  days_overdue: number;
  last_payment_date?: string;
}

interface CashFlowForecast {
  date: string;
  expected_amount: number;
  overdue_amount: number;
  total_expected: number;
}

interface CollectionPerformance {
  month: string;
  total_debt: number;
  collected_amount: number;
  collection_rate: number;
  average_collection_days: number;
}

interface DebtReportsProps {
  customerId?: number;
}

const DebtReports: React.FC<DebtReportsProps> = ({ customerId }) => {
  const [reportType, setReportType] = useState('customer_debts');
  const [dateFrom, setDateFrom] = useState('2024-01-01');
  const [dateTo, setDateTo] = useState('2024-12-31');
  const [customerFilter, setCustomerFilter] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);

  // بيانات وهمية للتقارير
  const [debtReports, setDebtReports] = useState<DebtReport[]>([]);
  const [cashFlowForecast, setCashFlowForecast] = useState<CashFlowForecast[]>([]);
  const [collectionPerformance, setCollectionPerformance] = useState<CollectionPerformance[]>([]);

  useEffect(() => {
    loadReportData();
  }, [reportType, dateFrom, dateTo, customerFilter]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل بيانات التقارير
      const mockDebtReports: DebtReport[] = [
        {
          customer_name: 'شركة النقل السريع',
          total_debt: 450000,
          paid_amount: 200000,
          remaining_amount: 250000,
          overdue_amount: 100000,
          days_overdue: 15,
          last_payment_date: '2024-05-20',
        },
        {
          customer_name: 'محمد بن علي - مالك أسطول',
          total_debt: 320000,
          paid_amount: 195000,
          remaining_amount: 125000,
          overdue_amount: 125000,
          days_overdue: 45,
          last_payment_date: '2024-04-15',
        },
        {
          customer_name: 'ورشة الأمين للصيانة',
          total_debt: 89000,
          paid_amount: 89000,
          remaining_amount: 0,
          overdue_amount: 0,
          days_overdue: 0,
          last_payment_date: '2024-05-25',
        },
        {
          customer_name: 'فاطمة الزهراء',
          total_debt: 25000,
          paid_amount: 0,
          remaining_amount: 25000,
          overdue_amount: 0,
          days_overdue: 0,
        },
      ];

      const mockCashFlow: CashFlowForecast[] = [
        { date: '2024-06-15', expected_amount: 150000, overdue_amount: 100000, total_expected: 250000 },
        { date: '2024-06-30', expected_amount: 75000, overdue_amount: 50000, total_expected: 125000 },
        { date: '2024-07-15', expected_amount: 200000, overdue_amount: 25000, total_expected: 225000 },
        { date: '2024-07-31', expected_amount: 100000, overdue_amount: 0, total_expected: 100000 },
        { date: '2024-08-15', expected_amount: 180000, overdue_amount: 0, total_expected: 180000 },
        { date: '2024-08-31', expected_amount: 90000, overdue_amount: 0, total_expected: 90000 },
      ];

      const mockPerformance: CollectionPerformance[] = [
        { month: 'يناير', total_debt: 500000, collected_amount: 450000, collection_rate: 90, average_collection_days: 25 },
        { month: 'فبراير', total_debt: 600000, collected_amount: 480000, collection_rate: 80, average_collection_days: 30 },
        { month: 'مارس', total_debt: 550000, collected_amount: 495000, collection_rate: 90, average_collection_days: 22 },
        { month: 'أبريل', total_debt: 700000, collected_amount: 560000, collection_rate: 80, average_collection_days: 35 },
        { month: 'مايو', total_debt: 650000, collected_amount: 585000, collection_rate: 90, average_collection_days: 28 },
        { month: 'يونيو', total_debt: 750000, collected_amount: 600000, collection_rate: 80, average_collection_days: 32 },
      ];

      setDebtReports(mockDebtReports);
      setCashFlowForecast(mockCashFlow);
      setCollectionPerformance(mockPerformance);
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // محاكاة تصدير التقرير
    const dataStr = JSON.stringify(debtReports, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `debt_report_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  // حساب الإحصائيات
  const totalDebts = debtReports.reduce((sum, report) => sum + report.total_debt, 0);
  const totalPaid = debtReports.reduce((sum, report) => sum + report.paid_amount, 0);
  const totalRemaining = debtReports.reduce((sum, report) => sum + report.remaining_amount, 0);
  const totalOverdue = debtReports.reduce((sum, report) => sum + report.overdue_amount, 0);

  // بيانات الرسم البياني الدائري
  const pieData = [
    { name: 'مدفوع', value: totalPaid, color: '#10b981' },
    { name: 'متبقي عادي', value: totalRemaining - totalOverdue, color: '#f59e0b' },
    { name: 'متأخر', value: totalOverdue, color: '#ef4444' },
  ];

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        تقارير الديون المتقدمة
      </Typography>

      {/* أدوات التحكم */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>نوع التقرير</InputLabel>
              <Select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                label="نوع التقرير"
              >
                <MenuItem value="customer_debts">ديون العملاء</MenuItem>
                <MenuItem value="overdue_debts">الديون المتأخرة</MenuItem>
                <MenuItem value="cash_flow">التدفق النقدي المتوقع</MenuItem>
                <MenuItem value="collection_performance">أداء التحصيل</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="من تاريخ"
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="إلى تاريخ"
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="تصفية العملاء"
              value={customerFilter}
              onChange={(e) => setCustomerFilter(e.target.value)}
              placeholder="اسم العميل..."
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="طباعة">
                <IconButton onClick={handlePrint} sx={{ color: '#3b82f6' }}>
                  <PrintIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="تصدير">
                <IconButton onClick={handleExport} sx={{ color: '#10b981' }}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* الإحصائيات السريعة */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
                {totalDebts.toLocaleString()}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي الديون (دج)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
                {totalPaid.toLocaleString()}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي المحصل (دج)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
                {totalRemaining.toLocaleString()}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي المتبقي (دج)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
                {totalOverdue.toLocaleString()}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي المتأخر (دج)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 500,
            },
          }}
        >
          <Tab
            icon={<AssessmentIcon />}
            label="تقرير تفصيلي"
            iconPosition="start"
          />
          <Tab
            icon={<TrendingUpIcon />}
            label="الرسوم البيانية"
            iconPosition="start"
          />
          <Tab
            icon={<ScheduleIcon />}
            label="التدفق النقدي"
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* التقرير التفصيلي */}
          {tabValue === 0 && (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      اسم العميل
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      إجمالي الدين
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ المدفوع
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ المتبقي
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      المبلغ المتأخر
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      أيام التأخير
                    </TableCell>
                    <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                      آخر دفعة
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {debtReports.map((report, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <PersonIcon sx={{ color: '#64748b', fontSize: 20 }} />
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                            {report.customer_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          {report.total_debt.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#10b981' }}>
                          {report.paid_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#f59e0b' }}>
                          {report.remaining_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#ef4444', fontWeight: 'bold' }}>
                          {report.overdue_amount.toLocaleString()} دج
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {report.days_overdue > 0 ? (
                          <Chip
                            label={`${report.days_overdue} يوم`}
                            size="small"
                            color="error"
                          />
                        ) : (
                          <Chip
                            label="لا يوجد"
                            size="small"
                            color="success"
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {report.last_payment_date 
                            ? new Date(report.last_payment_date).toLocaleDateString('ar-DZ')
                            : 'لا يوجد'
                          }
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* الرسوم البيانية */}
          {tabValue === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                      توزيع الديون
                    </Typography>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={pieData}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          >
                            {pieData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <RechartsTooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                      أداء التحصيل الشهري
                    </Typography>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={collectionPerformance}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <RechartsTooltip />
                          <Bar dataKey="collection_rate" fill="#10b981" name="معدل التحصيل %" />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* التدفق النقدي */}
          {tabValue === 2 && (
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
                  التدفق النقدي المتوقع
                </Typography>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={cashFlowForecast}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip />
                      <Line 
                        type="monotone" 
                        dataKey="expected_amount" 
                        stroke="#3b82f6" 
                        strokeWidth={2}
                        name="المبلغ المتوقع"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="overdue_amount" 
                        stroke="#ef4444" 
                        strokeWidth={2}
                        name="المبلغ المتأخر"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="total_expected" 
                        stroke="#10b981" 
                        strokeWidth={2}
                        name="الإجمالي المتوقع"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default DebtReports;
