import React, { useState } from 'react';
import {
  IconButton,
  <PERSON>ge,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsOff as NotificationsOffIcon,
  Circle as CircleIcon,
  Delete as DeleteIcon,
  DoneAll as DoneAllIcon,
  Clear as ClearIcon,
  Inventory as InventoryIcon,
  AccountBalance as DebtIcon,
  Receipt as InvoiceIcon,
  Info as InfoIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
} from '@mui/icons-material';
import { useNotifications, Notification } from '../../contexts/NotificationContext';
import { useNavigate } from 'react-router-dom';

const NotificationDropdown: React.FC = () => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    playNotificationSound,
    toggleNotificationSound,
  } = useNotifications();
  
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
    
    handleClose();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
        return <InventoryIcon sx={{ color: '#f59e0b' }} />;
      case 'overdue_debt':
        return <DebtIcon sx={{ color: '#ef4444' }} />;
      case 'pending_invoice':
        return <InvoiceIcon sx={{ color: '#3b82f6' }} />;
      case 'success':
        return <CircleIcon sx={{ color: '#10b981' }} />;
      case 'warning':
        return <CircleIcon sx={{ color: '#f59e0b' }} />;
      case 'error':
        return <CircleIcon sx={{ color: '#ef4444' }} />;
      default:
        return <InfoIcon sx={{ color: '#64748b' }} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'الآن';
    } else if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `منذ ${days} يوم`;
    }
  };

  // تجميع التنبيهات حسب التاريخ
  const groupedNotifications = notifications.reduce((groups: { [key: string]: Notification[] }, notification) => {
    const date = new Date(notification.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(notification);
    return groups;
  }, {});

  const getDateLabel = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    if (dateString === today) {
      return 'اليوم';
    } else if (dateString === yesterday) {
      return 'أمس';
    } else {
      return date.toLocaleDateString('ar-DZ');
    }
  };

  return (
    <>
      <Tooltip title="التنبيهات">
        <IconButton
          onClick={handleClick}
          sx={{
            color: 'inherit',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          }}
        >
          <Badge badgeContent={unreadCount} color="error">
            {unreadCount > 0 ? <NotificationsIcon /> : <NotificationsOffIcon />}
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 400,
            maxHeight: 600,
            mt: 1,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* رأس التنبيهات */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
              التنبيهات
            </Typography>
            <Chip
              label={`${unreadCount} غير مقروء`}
              size="small"
              color={unreadCount > 0 ? 'error' : 'default'}
            />
          </Box>

          {/* إعدادات التنبيهات */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <FormControlLabel
              control={
                <Switch
                  checked={playNotificationSound}
                  onChange={toggleNotificationSound}
                  size="small"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {playNotificationSound ? <VolumeUpIcon fontSize="small" /> : <VolumeOffIcon fontSize="small" />}
                  <Typography variant="caption">الصوت</Typography>
                </Box>
              }
              sx={{ m: 0 }}
            />

            <Box>
              {unreadCount > 0 && (
                <Tooltip title="وضع علامة مقروء على الكل">
                  <IconButton size="small" onClick={markAllAsRead}>
                    <DoneAllIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {notifications.length > 0 && (
                <Tooltip title="حذف جميع التنبيهات">
                  <IconButton size="small" onClick={clearAllNotifications}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>
        </Box>

        {/* قائمة التنبيهات */}
        <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
          {notifications.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <NotificationsOffIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
                لا توجد تنبيهات
              </Typography>
            </Box>
          ) : (
            Object.entries(groupedNotifications).map(([dateString, dayNotifications]) => (
              <Box key={dateString}>
                <Box sx={{ p: 1, backgroundColor: 'action.hover' }}>
                  <Typography variant="caption" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                    {getDateLabel(dateString)}
                  </Typography>
                </Box>
                <List dense>
                  {dayNotifications.map((notification) => (
                    <ListItem
                      key={notification.id}
                      button
                      onClick={() => handleNotificationClick(notification)}
                      sx={{
                        backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                        borderRight: `4px solid ${getPriorityColor(notification.priority)}`,
                        '&:hover': {
                          backgroundColor: 'action.selected',
                        },
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%', gap: 1 }}>
                        {getNotificationIcon(notification.type)}
                        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              fontFamily: 'Cairo, sans-serif',
                              fontWeight: notification.isRead ? 'normal' : 'bold',
                            }}
                          >
                            {notification.title}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'Cairo, sans-serif',
                              color: 'text.secondary',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                            {formatTimestamp(notification.timestamp)}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                          {!notification.isRead && (
                            <CircleIcon sx={{ fontSize: 8, color: 'primary.main' }} />
                          )}
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeNotification(notification.id);
                            }}
                            sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    </ListItem>
                  ))}
                </List>
                <Divider />
              </Box>
            ))
          )}
        </Box>

        {/* تذييل التنبيهات */}
        {notifications.length > 0 && (
          <Box sx={{ p: 1, borderTop: '1px solid', borderColor: 'divider' }}>
            <Button
              fullWidth
              size="small"
              onClick={() => {
                navigate('/notifications');
                handleClose();
              }}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              عرض جميع التنبيهات
            </Button>
          </Box>
        )}
      </Menu>
    </>
  );
};

export default NotificationDropdown;
