import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// تعريف أنواع البيانات
export interface Notification {
  id: string;
  type: 'low_stock' | 'overdue_debt' | 'pending_invoice' | 'system' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  isRead: boolean;
  timestamp: string;
  actionUrl?: string;
  data?: any;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  playNotificationSound: boolean;
  toggleNotificationSound: () => void;
}

interface NotificationProviderProps {
  children: ReactNode;
}

// إنشاء السياق
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Hook لاستخدام السياق
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// مكون مزود التنبيهات
export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>(() => {
    const saved = localStorage.getItem('notifications');
    return saved ? JSON.parse(saved) : [];
  });

  const [playNotificationSound, setPlayNotificationSound] = useState(() => {
    const saved = localStorage.getItem('notificationSound');
    return saved ? JSON.parse(saved) : true;
  });

  // حفظ التنبيهات في localStorage
  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(notifications));
  }, [notifications]);

  // حفظ إعداد الصوت في localStorage
  useEffect(() => {
    localStorage.setItem('notificationSound', JSON.stringify(playNotificationSound));
  }, [playNotificationSound]);

  // تشغيل صوت التنبيه
  const playSound = () => {
    if (playNotificationSound) {
      try {
        const audio = new Audio('/notification-sound.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
          // تجاهل الأخطاء إذا لم يتمكن من تشغيل الصوت
        });
      } catch (error) {
        // تجاهل الأخطاء
      }
    }
  };

  // إضافة تنبيه جديد
  const addNotification = (notificationData: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setNotifications(prev => [newNotification, ...prev]);

    // تشغيل صوت للتنبيهات عالية الأولوية
    if (notificationData.priority === 'high') {
      playSound();
    }
  };

  // وضع علامة مقروء على تنبيه
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      )
    );
  };

  // وضع علامة مقروء على جميع التنبيهات
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  // حذف تنبيه
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // حذف جميع التنبيهات
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // تبديل إعداد الصوت
  const toggleNotificationSound = () => {
    setPlayNotificationSound(!playNotificationSound);
  };

  // حساب عدد التنبيهات غير المقروءة
  const unreadCount = notifications.filter(notification => !notification.isRead).length;

  // تنظيف التنبيهات القديمة (أكثر من 30 يوم)
  useEffect(() => {
    const cleanupOldNotifications = () => {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      setNotifications(prev =>
        prev.filter(notification => new Date(notification.timestamp) > thirtyDaysAgo)
      );
    };

    // تشغيل التنظيف عند التحميل
    cleanupOldNotifications();

    // تشغيل التنظيف كل يوم
    const interval = setInterval(cleanupOldNotifications, 24 * 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  // إنشاء تنبيهات تلقائية للمخزون المنخفض والديون المتأخرة
  useEffect(() => {
    const checkForAutoNotifications = () => {
      // تحقق من المخزون المنخفض (محاكاة)
      const lowStockItems = [
        { name: 'فلتر هواء ENG001', quantity: 3, minQuantity: 10 },
        { name: 'قرص فرامل BRK001', quantity: 1, minQuantity: 5 },
      ];

      lowStockItems.forEach(item => {
        if (item.quantity < item.minQuantity) {
          // تحقق من عدم وجود تنبيه مماثل في آخر 24 ساعة
          const oneDayAgo = new Date();
          oneDayAgo.setDate(oneDayAgo.getDate() - 1);

          const existingNotification = notifications.find(n =>
            n.type === 'low_stock' &&
            n.data?.itemName === item.name &&
            new Date(n.timestamp) > oneDayAgo
          );

          if (!existingNotification) {
            addNotification({
              type: 'low_stock',
              title: 'مخزون منخفض',
              message: `${item.name} - الكمية المتبقية: ${item.quantity}`,
              priority: 'high',
              actionUrl: '/inventory',
              data: { itemName: item.name, quantity: item.quantity, minQuantity: item.minQuantity },
            });
          }
        }
      });

      // تحقق من الديون المتأخرة (محاكاة)
      const overdueDebts = [
        { customerName: 'محمد بن علي', amount: 125000, dueDate: '2024-05-25' },
      ];

      overdueDebts.forEach(debt => {
        const dueDate = new Date(debt.dueDate);
        const today = new Date();

        if (dueDate < today) {
          // تحقق من عدم وجود تنبيه مماثل في آخر 7 أيام
          const sevenDaysAgo = new Date();
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

          const existingNotification = notifications.find(n =>
            n.type === 'overdue_debt' &&
            n.data?.customerName === debt.customerName &&
            new Date(n.timestamp) > sevenDaysAgo
          );

          if (!existingNotification) {
            const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
            addNotification({
              type: 'overdue_debt',
              title: 'دين متأخر',
              message: `${debt.customerName} - ${debt.amount.toLocaleString()} دج (متأخر ${daysPastDue} يوم)`,
              priority: 'high',
              actionUrl: '/debts',
              data: { customerName: debt.customerName, amount: debt.amount, daysPastDue },
            });
          }
        }
      });
    };

    // تشغيل الفحص عند التحميل
    checkForAutoNotifications();

    // تشغيل الفحص كل ساعة
    const interval = setInterval(checkForAutoNotifications, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [notifications]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    playNotificationSound,
    toggleNotificationSound,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
