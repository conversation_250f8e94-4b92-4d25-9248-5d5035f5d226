import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider, Theme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// تعريف أنواع البيانات
interface ThemeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  theme: Theme;
}

interface ThemeProviderProps {
  children: ReactNode;
}

// إنشاء السياق
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Hook لاستخدام السياق
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// إنشاء الثيم الفاتح
const lightTheme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'light',
    primary: {
      main: '#3b82f6',
      light: '#60a5fa',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#10b981',
      light: '#34d399',
      dark: '#059669',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
    text: {
      primary: '#1e293b',
      secondary: '#64748b',
    },
    error: {
      main: '#ef4444',
    },
    warning: {
      main: '#f59e0b',
    },
    info: {
      main: '#06b6d4',
    },
    success: {
      main: '#10b981',
    },
  },
  typography: {
    fontFamily: 'Cairo, sans-serif',
    h1: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 700,
    },
    h2: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h3: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h4: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h5: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
    h6: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
    body1: {
      fontFamily: 'Cairo, sans-serif',
    },
    body2: {
      fontFamily: 'Cairo, sans-serif',
    },
    button: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: 'Cairo, sans-serif',
          direction: 'rtl',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontFamily: 'Cairo, sans-serif',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        },
      },
    },
  },
});

// إنشاء الثيم الداكن
const darkTheme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'dark',
    primary: {
      main: '#60a5fa',
      light: '#93c5fd',
      dark: '#3b82f6',
    },
    secondary: {
      main: '#34d399',
      light: '#6ee7b7',
      dark: '#10b981',
    },
    background: {
      default: '#0f172a',
      paper: '#1e293b',
    },
    text: {
      primary: '#f1f5f9',
      secondary: '#cbd5e1',
    },
    error: {
      main: '#f87171',
    },
    warning: {
      main: '#fbbf24',
    },
    info: {
      main: '#22d3ee',
    },
    success: {
      main: '#34d399',
    },
  },
  typography: {
    fontFamily: 'Cairo, sans-serif',
    h1: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 700,
    },
    h2: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h3: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h4: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
    },
    h5: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
    h6: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
    body1: {
      fontFamily: 'Cairo, sans-serif',
    },
    body2: {
      fontFamily: 'Cairo, sans-serif',
    },
    button: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: 'Cairo, sans-serif',
          direction: 'rtl',
          backgroundColor: '#0f172a',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontFamily: 'Cairo, sans-serif',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: '#1e293b',
          backgroundImage: 'none',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: '#1e293b',
          backgroundImage: 'none',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1e293b',
          borderColor: '#334155',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e293b',
          borderColor: '#334155',
        },
      },
    },
    MuiDataGrid: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e293b',
          border: '1px solid #334155',
          '& .MuiDataGrid-cell': {
            borderColor: '#334155',
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#334155',
            borderColor: '#334155',
          },
          '& .MuiDataGrid-footerContainer': {
            backgroundColor: '#334155',
            borderColor: '#334155',
          },
        },
      },
    },
  },
});

// مكون مزود الثيم
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : false;
  });

  useEffect(() => {
    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  const value: ThemeContextType = {
    isDarkMode,
    toggleDarkMode,
    theme,
  };

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};
