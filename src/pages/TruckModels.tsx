import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  CarRepair as TruckIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface TruckModel {
  id: number;
  model_name: string;
  manufacturer: string;
  production_year: number;
  engine_type: string;
  engine_capacity: string;
  fuel_type: string;
  transmission: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// مكون نموذج إضافة/تعديل موديل
interface TruckModelDialogProps {
  open: boolean;
  onClose: () => void;
  truckModel?: TruckModel | null;
  onSave: (truckModel: Partial<TruckModel>) => void;
}

const TruckModelDialog: React.FC<TruckModelDialogProps> = ({ open, onClose, truckModel, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<TruckModel>>({
    model_name: '',
    manufacturer: '',
    production_year: new Date().getFullYear(),
    engine_type: '',
    engine_capacity: '',
    fuel_type: 'ديزل',
    transmission: 'يدوي',
    is_active: true,
  });

  useEffect(() => {
    if (truckModel) {
      setFormData(truckModel);
    } else {
      setFormData({
        model_name: '',
        manufacturer: '',
        production_year: new Date().getFullYear(),
        engine_type: '',
        engine_capacity: '',
        fuel_type: 'ديزل',
        transmission: 'يدوي',
        is_active: true,
      });
    }
  }, [truckModel, open]);

  const handleChange = (field: keyof TruckModel) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {truckModel ? t('editTruckModel') : t('addTruckModel')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('modelName')}
              value={formData.model_name || ''}
              onChange={handleChange('model_name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('manufacturer')}
              value={formData.manufacturer || ''}
              onChange={handleChange('manufacturer')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('productionYear')}
              type="number"
              value={formData.production_year || new Date().getFullYear()}
              onChange={handleChange('production_year')}
              inputProps={{ min: 1980, max: new Date().getFullYear() + 1 }}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('engineType')}
              value={formData.engine_type || ''}
              onChange={handleChange('engine_type')}
              placeholder="مثال: V6, V8, I4"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('engineCapacity')}
              value={formData.engine_capacity || ''}
              onChange={handleChange('engine_capacity')}
              placeholder="مثال: 3.0L, 5.7L"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('fuelType')}</InputLabel>
              <Select
                value={formData.fuel_type || 'ديزل'}
                onChange={handleChange('fuel_type')}
                label={t('fuelType')}
              >
                <MenuItem value="ديزل">ديزل</MenuItem>
                <MenuItem value="بنزين">بنزين</MenuItem>
                <MenuItem value="غاز طبيعي">غاز طبيعي</MenuItem>
                <MenuItem value="كهربائي">كهربائي</MenuItem>
                <MenuItem value="هجين">هجين</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('transmission')}</InputLabel>
              <Select
                value={formData.transmission || 'يدوي'}
                onChange={handleChange('transmission')}
                label={t('transmission')}
              >
                <MenuItem value="يدوي">يدوي</MenuItem>
                <MenuItem value="أوتوماتيكي">أوتوماتيكي</MenuItem>
                <MenuItem value="نصف أوتوماتيكي">نصف أوتوماتيكي</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={formData.is_active ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 1 }))}
                label="الحالة"
              >
                <MenuItem value={1}>نشط</MenuItem>
                <MenuItem value={0}>غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const TruckModels: React.FC = () => {
  const { t } = useTranslation();
  const [truckModels, setTruckModels] = useState<TruckModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [manufacturerFilter, setManufacturerFilter] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTruckModel, setSelectedTruckModel] = useState<TruckModel | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'model_name',
      headerName: 'اسم الموديل',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TruckIcon sx={{ mr: 1, color: '#64748b' }} />
          <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'manufacturer',
      headerName: 'الشركة المصنعة',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'production_year',
      headerName: 'سنة الإنتاج',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'engine_type',
      headerName: 'نوع المحرك',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'fuel_type',
      headerName: 'نوع الوقود',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          color={params.value === 'ديزل' ? 'primary' : params.value === 'بنزين' ? 'secondary' : 'default'}
        />
      ),
    },
    {
      field: 'transmission',
      headerName: 'ناقل الحركة',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'is_active',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          size="small"
          color={params.value ? 'success' : 'default'}
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 120,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#3b82f6' }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadTruckModels();
  }, []);

  const loadTruckModels = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockTruckModels: TruckModel[] = [
        {
          id: 1,
          model_name: 'Actros 1845',
          manufacturer: 'Mercedes-Benz',
          production_year: 2022,
          engine_type: 'OM 471',
          engine_capacity: '12.8L',
          fuel_type: 'ديزل',
          transmission: 'أوتوماتيكي',
          is_active: true,
          created_at: '2024-01-15',
          updated_at: '2024-05-20',
        },
        {
          id: 2,
          model_name: 'FH16 750',
          manufacturer: 'Volvo',
          production_year: 2023,
          engine_type: 'D16K',
          engine_capacity: '16.1L',
          fuel_type: 'ديزل',
          transmission: 'أوتوماتيكي',
          is_active: true,
          created_at: '2024-02-10',
          updated_at: '2024-05-18',
        },
        {
          id: 3,
          model_name: 'R 450',
          manufacturer: 'Scania',
          production_year: 2021,
          engine_type: 'DC13',
          engine_capacity: '13.0L',
          fuel_type: 'ديزل',
          transmission: 'يدوي',
          is_active: true,
          created_at: '2024-01-20',
          updated_at: '2024-05-15',
        },
        {
          id: 4,
          model_name: 'Stralis XP 570',
          manufacturer: 'Iveco',
          production_year: 2020,
          engine_type: 'Cursor 13',
          engine_capacity: '12.9L',
          fuel_type: 'ديزل',
          transmission: 'أوتوماتيكي',
          is_active: true,
          created_at: '2024-03-05',
          updated_at: '2024-05-12',
        },
        {
          id: 5,
          model_name: 'CF 440',
          manufacturer: 'DAF',
          production_year: 2019,
          engine_type: 'MX-13',
          engine_capacity: '12.9L',
          fuel_type: 'ديزل',
          transmission: 'يدوي',
          is_active: false,
          created_at: '2024-01-08',
          updated_at: '2024-04-30',
        },
        {
          id: 6,
          model_name: 'Arocs 3348',
          manufacturer: 'Mercedes-Benz',
          production_year: 2023,
          engine_type: 'OM 473',
          engine_capacity: '15.6L',
          fuel_type: 'ديزل',
          transmission: 'أوتوماتيكي',
          is_active: true,
          created_at: '2024-04-12',
          updated_at: '2024-05-25',
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setTruckModels(mockTruckModels);
    } catch (error) {
      console.error('Error loading truck models:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedTruckModel(null);
    setDialogOpen(true);
  };

  const handleEdit = (truckModel: TruckModel) => {
    setSelectedTruckModel(truckModel);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الموديل؟')) {
      try {
        // هنا سيتم حذف الموديل من قاعدة البيانات
        setTruckModels(prev => prev.filter(model => model.id !== id));
        setSnackbar({ open: true, message: 'تم حذف الموديل بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف الموديل', severity: 'error' });
      }
    }
  };

  const handleSave = async (truckModelData: Partial<TruckModel>) => {
    try {
      if (selectedTruckModel) {
        // تحديث موديل موجود
        setTruckModels(prev => prev.map(model =>
          model.id === selectedTruckModel.id ? { ...model, ...truckModelData, updated_at: new Date().toISOString().split('T')[0] } : model
        ));
        setSnackbar({ open: true, message: 'تم تحديث الموديل بنجاح', severity: 'success' });
      } else {
        // إضافة موديل جديد
        const newTruckModel: TruckModel = {
          id: Date.now(),
          created_at: new Date().toISOString().split('T')[0],
          updated_at: new Date().toISOString().split('T')[0],
          ...truckModelData as TruckModel,
        };
        setTruckModels(prev => [...prev, newTruckModel]);
        setSnackbar({ open: true, message: 'تم إضافة الموديل بنجاح', severity: 'success' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ الموديل', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredTruckModels = truckModels.filter(model => {
    const matchesSearch = model.model_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.manufacturer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesManufacturer = !manufacturerFilter || model.manufacturer === manufacturerFilter;
    const matchesYear = !yearFilter || model.production_year.toString() === yearFilter;
    return matchesSearch && matchesManufacturer && matchesYear;
  });

  // حساب الإحصائيات
  const totalModels = truckModels.length;
  const activeModels = truckModels.filter(model => model.is_active).length;
  const manufacturers = [...new Set(truckModels.map(model => model.manufacturer))];
  const years = [...new Set(truckModels.map(model => model.production_year))].sort((a, b) => b - a);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('truckModels')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalModels}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الموديلات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {activeModels}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              موديلات نشطة
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {manufacturers.length}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              شركات مصنعة
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#8b5cf6', fontWeight: 'bold' }}>
              {new Date().getFullYear() - Math.min(...years) + 1}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              سنوات الإنتاج
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="البحث في الموديلات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب الشركة المصنعة</InputLabel>
              <Select
                value={manufacturerFilter}
                onChange={(e) => setManufacturerFilter(e.target.value)}
                label="تصفية حسب الشركة المصنعة"
              >
                <MenuItem value="">جميع الشركات</MenuItem>
                {manufacturers.map(manufacturer => (
                  <MenuItem key={manufacturer} value={manufacturer}>{manufacturer}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب سنة الإنتاج</InputLabel>
              <Select
                value={yearFilter}
                onChange={(e) => setYearFilter(e.target.value)}
                label="تصفية حسب سنة الإنتاج"
              >
                <MenuItem value="">جميع السنوات</MenuItem>
                {years.map(year => (
                  <MenuItem key={year} value={year.toString()}>{year}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addTruckModel')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredTruckModels}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* نموذج إضافة/تعديل */}
      <TruckModelDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        truckModel={selectedTruckModel}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TruckModels;
