import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Chip,
  Divider,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  People as UsersIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  Language as LanguageIcon,
  Palette as ThemeIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'employee';
  isActive: boolean;
  lastLogin: string;
}

interface AppSettings {
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  currency: string;
  taxRate: number;
  language: string;
  theme: string;
  autoBackup: boolean;
  backupInterval: number;
  lowStockAlert: boolean;
  lowStockThreshold: number;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

// مكون الإعدادات العامة
const GeneralSettings: React.FC = () => {
  const [settings, setSettings] = useState<AppSettings>({
    companyName: 'متجر قطع غيار الشاحنات',
    companyAddress: 'الجزائر العاصمة، الجزائر',
    companyPhone: '**********',
    companyEmail: '<EMAIL>',
    currency: 'DZD',
    taxRate: 19,
    language: 'ar',
    theme: 'light',
    autoBackup: true,
    backupInterval: 24,
    lowStockAlert: true,
    lowStockThreshold: 10,
    emailNotifications: true,
    smsNotifications: false,
  });

  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const handleSettingChange = (field: keyof AppSettings) => (event: any) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    // هنا سيتم حفظ الإعدادات في قاعدة البيانات
    console.log('Saving settings:', settings);
    setSnackbar({ open: true, message: 'تم حفظ الإعدادات بنجاح', severity: 'success' });
  };

  return (
    <Grid container spacing={3}>
      {/* معلومات الشركة */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              معلومات الشركة
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="اسم الشركة"
                  value={settings.companyName}
                  onChange={handleSettingChange('companyName')}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="عنوان الشركة"
                  value={settings.companyAddress}
                  onChange={handleSettingChange('companyAddress')}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="هاتف الشركة"
                  value={settings.companyPhone}
                  onChange={handleSettingChange('companyPhone')}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="بريد الشركة الإلكتروني"
                  type="email"
                  value={settings.companyEmail}
                  onChange={handleSettingChange('companyEmail')}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* الإعدادات المالية */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              الإعدادات المالية
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>العملة</InputLabel>
                  <Select
                    value={settings.currency}
                    onChange={handleSettingChange('currency')}
                    label="العملة"
                  >
                    <MenuItem value="DZD">دينار جزائري (DZD)</MenuItem>
                    <MenuItem value="USD">دولار أمريكي (USD)</MenuItem>
                    <MenuItem value="EUR">يورو (EUR)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="معدل الضريبة (%)"
                  type="number"
                  value={settings.taxRate}
                  onChange={handleSettingChange('taxRate')}
                  inputProps={{ min: 0, max: 100 }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* إعدادات التطبيق */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              إعدادات التطبيق
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>اللغة</InputLabel>
                  <Select
                    value={settings.language}
                    onChange={handleSettingChange('language')}
                    label="اللغة"
                  >
                    <MenuItem value="ar">العربية</MenuItem>
                    <MenuItem value="en">English</MenuItem>
                    <MenuItem value="fr">Français</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>المظهر</InputLabel>
                  <Select
                    value={settings.theme}
                    onChange={handleSettingChange('theme')}
                    label="المظهر"
                  >
                    <MenuItem value="light">فاتح</MenuItem>
                    <MenuItem value="dark">داكن</MenuItem>
                    <MenuItem value="auto">تلقائي</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* إعدادات التنبيهات */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              إعدادات التنبيهات
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.lowStockAlert}
                      onChange={handleSettingChange('lowStockAlert')}
                    />
                  }
                  label="تنبيهات المخزون المنخفض"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="حد التنبيه للمخزون المنخفض"
                  type="number"
                  value={settings.lowStockThreshold}
                  onChange={handleSettingChange('lowStockThreshold')}
                  disabled={!settings.lowStockAlert}
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.emailNotifications}
                      onChange={handleSettingChange('emailNotifications')}
                    />
                  }
                  label="تنبيهات البريد الإلكتروني"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.smsNotifications}
                      onChange={handleSettingChange('smsNotifications')}
                    />
                  }
                  label="تنبيهات الرسائل النصية"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* إعدادات النسخ الاحتياطي */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              إعدادات النسخ الاحتياطي
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.autoBackup}
                      onChange={handleSettingChange('autoBackup')}
                    />
                  }
                  label="النسخ الاحتياطي التلقائي"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="فترة النسخ الاحتياطي (ساعة)"
                  type="number"
                  value={settings.backupInterval}
                  onChange={handleSettingChange('backupInterval')}
                  disabled={!settings.autoBackup}
                  inputProps={{ min: 1, max: 168 }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<BackupIcon />}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  إنشاء نسخة احتياطية الآن
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* زر الحفظ */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            onClick={handleSave}
            sx={{ fontFamily: 'Cairo, sans-serif', px: 4 }}
          >
            حفظ الإعدادات
          </Button>
        </Box>
      </Grid>

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Grid>
  );
};

// مكون إدارة المستخدمين
const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true,
      lastLogin: '2024-05-29 10:30:00',
    },
    {
      id: 2,
      username: 'manager1',
      email: '<EMAIL>',
      role: 'manager',
      isActive: true,
      lastLogin: '2024-05-28 14:15:00',
    },
    {
      id: 3,
      username: 'employee1',
      email: '<EMAIL>',
      role: 'employee',
      isActive: false,
      lastLogin: '2024-05-25 09:00:00',
    },
  ]);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير عام';
      case 'manager': return 'مدير';
      case 'employee': return 'موظف';
      default: return role;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'error';
      case 'manager': return 'warning';
      case 'employee': return 'info';
      default: return 'default';
    }
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setDialogOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  const handleDeleteUser = (userId: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(prev => prev.filter(user => user.id !== userId));
      setSnackbar({ open: true, message: 'تم حذف المستخدم بنجاح', severity: 'success' });
    }
  };

  return (
    <Box>
      {/* شريط الأدوات */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          إدارة المستخدمين
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إضافة مستخدم
        </Button>
      </Box>

      {/* قائمة المستخدمين */}
      <Paper>
        <List>
          {users.map((user, index) => (
            <React.Fragment key={user.id}>
              <ListItem>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                        {user.username}
                      </Typography>
                      <Chip
                        label={getRoleText(user.role)}
                        size="small"
                        color={getRoleColor(user.role) as any}
                      />
                      <Chip
                        label={user.isActive ? 'نشط' : 'غير نشط'}
                        size="small"
                        color={user.isActive ? 'success' : 'default'}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                        {user.email}
                      </Typography>
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b', fontSize: '0.875rem' }}>
                        آخر دخول: {new Date(user.lastLogin).toLocaleString('ar-DZ')}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => handleEditUser(user)}
                    sx={{ color: '#3b82f6', mr: 1 }}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    edge="end"
                    onClick={() => handleDeleteUser(user.id)}
                    sx={{ color: '#ef4444' }}
                    disabled={user.role === 'admin'}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
              {index < users.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

// مكون النسخ الاحتياطي والاستعادة
const BackupRestore: React.FC = () => {
  const [backups] = useState([
    { id: 1, name: 'backup_2024-05-29.db', date: '2024-05-29 10:00:00', size: '2.5 MB' },
    { id: 2, name: 'backup_2024-05-28.db', date: '2024-05-28 10:00:00', size: '2.4 MB' },
    { id: 3, name: 'backup_2024-05-27.db', date: '2024-05-27 10:00:00', size: '2.3 MB' },
  ]);

  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const handleCreateBackup = () => {
    // هنا سيتم إنشاء نسخة احتياطية
    setSnackbar({ open: true, message: 'تم إنشاء النسخة الاحتياطية بنجاح', severity: 'success' });
  };

  const handleRestoreBackup = (backupName: string) => {
    if (window.confirm(`هل أنت متأكد من استعادة النسخة الاحتياطية: ${backupName}؟`)) {
      // هنا سيتم استعادة النسخة الاحتياطية
      setSnackbar({ open: true, message: 'تم استعادة النسخة الاحتياطية بنجاح', severity: 'success' });
    }
  };

  return (
    <Grid container spacing={3}>
      {/* إنشاء نسخة احتياطية */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              إنشاء نسخة احتياطية
            </Typography>
            <Typography sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              قم بإنشاء نسخة احتياطية من جميع بيانات التطبيق
            </Typography>
            <Button
              variant="contained"
              fullWidth
              startIcon={<BackupIcon />}
              onClick={handleCreateBackup}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              إنشاء نسخة احتياطية
            </Button>
          </CardContent>
        </Card>
      </Grid>

      {/* استعادة من ملف */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              استعادة من ملف
            </Typography>
            <Typography sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              اختر ملف نسخة احتياطية لاستعادة البيانات
            </Typography>
            <Button
              variant="outlined"
              fullWidth
              startIcon={<RestoreIcon />}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              اختيار ملف
            </Button>
          </CardContent>
        </Card>
      </Grid>

      {/* قائمة النسخ الاحتياطية */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              النسخ الاحتياطية المحفوظة
            </Typography>
            <List>
              {backups.map((backup, index) => (
                <React.Fragment key={backup.id}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                          {backup.name}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                            التاريخ: {new Date(backup.date).toLocaleString('ar-DZ')}
                          </Typography>
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                            الحجم: {backup.size}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleRestoreBackup(backup.name)}
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        استعادة
                      </Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < backups.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Grid>
  );
};

const Settings: React.FC = () => {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('settings')}
      </Typography>

      {/* تبويبات الإعدادات */}
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 500,
            },
          }}
        >
          <Tab
            icon={<SettingsIcon />}
            label="الإعدادات العامة"
            iconPosition="start"
          />
          <Tab
            icon={<UsersIcon />}
            label="إدارة المستخدمين"
            iconPosition="start"
          />
          <Tab
            icon={<BackupIcon />}
            label="النسخ الاحتياطي"
            iconPosition="start"
          />
        </Tabs>

        {/* محتوى التبويبات */}
        <Box sx={{ p: 3 }}>
          {tabValue === 0 && <GeneralSettings />}
          {tabValue === 1 && <UserManagement />}
          {tabValue === 2 && <BackupRestore />}
        </Box>
      </Paper>
    </Box>
  );
};

export default Settings;