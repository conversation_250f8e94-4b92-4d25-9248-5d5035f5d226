import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Divider,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  DoneAll as DoneAllIcon,
  Clear as ClearIcon,
  Inventory as InventoryIcon,
  AccountBalance as DebtIcon,
  Receipt as InvoiceIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Notifications as NotificationsIcon,
  NotificationsOff as NotificationsOffIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNotifications, Notification } from '../contexts/NotificationContext';
import { useNavigate } from 'react-router-dom';

const Notifications: React.FC = () => {
  const { t } = useTranslation();
  const {
    notifications,
    unreadCount,
    mark<PERSON>Read,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
  } = useNotifications();
  
  const navigate = useNavigate();
  const [filterType, setFilterType] = useState('');
  const [filterPriority, setFilterPriority] = useState('');
  const [filterRead, setFilterRead] = useState('');

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
        return <InventoryIcon sx={{ color: '#f59e0b' }} />;
      case 'overdue_debt':
        return <DebtIcon sx={{ color: '#ef4444' }} />;
      case 'pending_invoice':
        return <InvoiceIcon sx={{ color: '#3b82f6' }} />;
      case 'success':
        return <CheckIcon sx={{ color: '#10b981' }} />;
      case 'warning':
        return <WarningIcon sx={{ color: '#f59e0b' }} />;
      case 'error':
        return <ErrorIcon sx={{ color: '#ef4444' }} />;
      default:
        return <InfoIcon sx={{ color: '#64748b' }} />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'low_stock':
        return 'مخزون منخفض';
      case 'overdue_debt':
        return 'دين متأخر';
      case 'pending_invoice':
        return 'فاتورة معلقة';
      case 'success':
        return 'نجاح';
      case 'warning':
        return 'تحذير';
      case 'error':
        return 'خطأ';
      case 'system':
        return 'نظام';
      default:
        return type;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('ar-DZ');
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  // تصفية التنبيهات
  const filteredNotifications = notifications.filter(notification => {
    const matchesType = !filterType || notification.type === filterType;
    const matchesPriority = !filterPriority || notification.priority === filterPriority;
    const matchesRead = !filterRead || 
      (filterRead === 'read' && notification.isRead) ||
      (filterRead === 'unread' && !notification.isRead);
    
    return matchesType && matchesPriority && matchesRead;
  });

  // تجميع التنبيهات حسب النوع
  const notificationsByType = notifications.reduce((acc, notification) => {
    acc[notification.type] = (acc[notification.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('notifications')}
      </Typography>

      {/* إحصائيات التنبيهات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
                {notifications.length}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                إجمالي التنبيهات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
                {unreadCount}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                غير مقروءة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
                {notificationsByType.low_stock || 0}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                مخزون منخفض
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
                {notificationsByType.overdue_debt || 0}
              </Typography>
              <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                ديون متأخرة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* أدوات التحكم */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>نوع التنبيه</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="نوع التنبيه"
              >
                <MenuItem value="">جميع الأنواع</MenuItem>
                <MenuItem value="low_stock">مخزون منخفض</MenuItem>
                <MenuItem value="overdue_debt">دين متأخر</MenuItem>
                <MenuItem value="pending_invoice">فاتورة معلقة</MenuItem>
                <MenuItem value="system">نظام</MenuItem>
                <MenuItem value="success">نجاح</MenuItem>
                <MenuItem value="warning">تحذير</MenuItem>
                <MenuItem value="error">خطأ</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>الأولوية</InputLabel>
              <Select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                label="الأولوية"
              >
                <MenuItem value="">جميع الأولويات</MenuItem>
                <MenuItem value="high">عالية</MenuItem>
                <MenuItem value="medium">متوسطة</MenuItem>
                <MenuItem value="low">منخفضة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>حالة القراءة</InputLabel>
              <Select
                value={filterRead}
                onChange={(e) => setFilterRead(e.target.value)}
                label="حالة القراءة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="unread">غير مقروءة</MenuItem>
                <MenuItem value="read">مقروءة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3} sx={{ display: 'flex', gap: 1 }}>
            {unreadCount > 0 && (
              <Tooltip title="وضع علامة مقروء على الكل">
                <Button
                  variant="outlined"
                  startIcon={<DoneAllIcon />}
                  onClick={markAllAsRead}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  قراءة الكل
                </Button>
              </Tooltip>
            )}
            {notifications.length > 0 && (
              <Tooltip title="حذف جميع التنبيهات">
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<ClearIcon />}
                  onClick={clearAllNotifications}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  حذف الكل
                </Button>
              </Tooltip>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* قائمة التنبيهات */}
      <Paper>
        {filteredNotifications.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <NotificationsOffIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary', mb: 1 }}>
              لا توجد تنبيهات
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: 'text.secondary' }}>
              {notifications.length === 0 
                ? 'لم يتم إنشاء أي تنبيهات بعد'
                : 'لا توجد تنبيهات تطابق المرشحات المحددة'
              }
            </Typography>
          </Box>
        ) : (
          <List>
            {filteredNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  button
                  onClick={() => handleNotificationClick(notification)}
                  sx={{
                    backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                    borderRight: `4px solid ${getPriorityColor(notification.priority)}`,
                    '&:hover': {
                      backgroundColor: 'action.selected',
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%', gap: 2 }}>
                    {getNotificationIcon(notification.type)}
                    <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontFamily: 'Cairo, sans-serif',
                            fontWeight: notification.isRead ? 'normal' : 'bold',
                          }}
                        >
                          {notification.title}
                        </Typography>
                        <Chip
                          label={getTypeLabel(notification.type)}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={getPriorityLabel(notification.priority)}
                          size="small"
                          sx={{ 
                            backgroundColor: getPriorityColor(notification.priority),
                            color: 'white',
                          }}
                        />
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'Cairo, sans-serif',
                          color: 'text.secondary',
                          mb: 0.5,
                        }}
                      >
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        {formatTimestamp(notification.timestamp)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                      {!notification.isRead && (
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'primary.main' }} />
                      )}
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                        sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                </ListItem>
                {index < filteredNotifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Paper>
    </Box>
  );
};

export default Notifications;
