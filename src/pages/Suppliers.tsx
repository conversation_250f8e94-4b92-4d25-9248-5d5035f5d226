import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Rating,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface Supplier {
  id: number;
  name: string;
  contact_person: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  country: string;
  rating: number;
  lead_time_days: number;
  payment_terms: string;
  total_orders: number;
  total_amount: number;
  last_order_date: string;
  notes: string;
  is_active: boolean;
}

// مكون نموذج إضافة/تعديل مورد
interface SupplierDialogProps {
  open: boolean;
  onClose: () => void;
  supplier?: Supplier | null;
  onSave: (supplier: Partial<Supplier>) => void;
}

const SupplierDialog: React.FC<SupplierDialogProps> = ({ open, onClose, supplier, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Supplier>>({
    name: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    country: 'الجزائر',
    rating: 3,
    lead_time_days: 7,
    payment_terms: '30 يوم',
    notes: '',
    is_active: true,
  });

  useEffect(() => {
    if (supplier) {
      setFormData(supplier);
    } else {
      setFormData({
        name: '',
        contact_person: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        country: 'الجزائر',
        rating: 3,
        lead_time_days: 7,
        payment_terms: '30 يوم',
        notes: '',
        is_active: true,
      });
    }
  }, [supplier, open]);

  const handleChange = (field: keyof Supplier) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {supplier ? t('editSupplier') : t('addSupplier')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('supplierName')}
              value={formData.name || ''}
              onChange={handleChange('name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('contactPerson')}
              value={formData.contact_person || ''}
              onChange={handleChange('contact_person')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('phone')}
              value={formData.phone || ''}
              onChange={handleChange('phone')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('email')}
              type="email"
              value={formData.email || ''}
              onChange={handleChange('email')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('address')}
              value={formData.address || ''}
              onChange={handleChange('address')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('city')}
              value={formData.city || ''}
              onChange={handleChange('city')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('country')}
              value={formData.country || ''}
              onChange={handleChange('country')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography component="legend" sx={{ fontFamily: 'Cairo, sans-serif', mb: 1 }}>
                {t('rating')}
              </Typography>
              <Rating
                value={formData.rating || 3}
                onChange={(_, newValue) => {
                  setFormData(prev => ({ ...prev, rating: newValue || 3 }));
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('leadTime')}
              type="number"
              value={formData.lead_time_days || 7}
              onChange={handleChange('lead_time_days')}
              InputProps={{
                endAdornment: <Typography variant="body2">يوم</Typography>,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('paymentTerms')}</InputLabel>
              <Select
                value={formData.payment_terms || '30 يوم'}
                onChange={handleChange('payment_terms')}
                label={t('paymentTerms')}
              >
                <MenuItem value="فوري">فوري</MenuItem>
                <MenuItem value="15 يوم">15 يوم</MenuItem>
                <MenuItem value="30 يوم">30 يوم</MenuItem>
                <MenuItem value="45 يوم">45 يوم</MenuItem>
                <MenuItem value="60 يوم">60 يوم</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات"
              multiline
              rows={3}
              value={formData.notes || ''}
              onChange={handleChange('notes')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={formData.is_active ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 1 }))}
                label="الحالة"
              >
                <MenuItem value={1}>نشط</MenuItem>
                <MenuItem value={0}>غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Suppliers: React.FC = () => {
  const { t } = useTranslation();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم المورد',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <BusinessIcon sx={{ mr: 1, color: '#64748b' }} />
          <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'contact_person',
      headerName: 'شخص الاتصال',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'phone',
      headerName: 'الهاتف',
      width: 130,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'city',
      headerName: 'المدينة',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'rating',
      headerName: 'التقييم',
      width: 120,
      renderCell: (params) => (
        <Rating value={params.value} readOnly size="small" />
      ),
    },
    {
      field: 'lead_time_days',
      headerName: 'مهلة التوريد',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value} يوم
        </Typography>
      ),
    },
    {
      field: 'total_amount',
      headerName: 'إجمالي الطلبات',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'is_active',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          size="small"
          color={params.value ? 'success' : 'default'}
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 120,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#3b82f6' }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockSuppliers: Supplier[] = [
        {
          id: 1,
          name: 'شركة قطع الغيار المتحدة',
          contact_person: 'أحمد بن محمد',
          phone: '021-123456',
          email: '<EMAIL>',
          address: 'المنطقة الصناعية الرويبة',
          city: 'الجزائر',
          country: 'الجزائر',
          rating: 5,
          lead_time_days: 5,
          payment_terms: '30 يوم',
          total_orders: 45,
          total_amount: 2500000,
          last_order_date: '2024-05-25',
          notes: 'مورد موثوق، جودة عالية',
          is_active: true,
        },
        {
          id: 2,
          name: 'مؤسسة الشرق للاستيراد',
          contact_person: 'فاطمة العلوي',
          phone: '031-987654',
          email: '<EMAIL>',
          address: 'حي الصناعات',
          city: 'وهران',
          country: 'الجزائر',
          rating: 4,
          lead_time_days: 10,
          payment_terms: '45 يوم',
          total_orders: 32,
          total_amount: 1800000,
          last_order_date: '2024-05-20',
          notes: 'أسعار تنافسية',
          is_active: true,
        },
        {
          id: 3,
          name: 'شركة الأطلس للتجارة',
          contact_person: 'محمد الصادق',
          phone: '041-555123',
          email: '<EMAIL>',
          address: 'المنطقة التجارية',
          city: 'قسنطينة',
          country: 'الجزائر',
          rating: 3,
          lead_time_days: 15,
          payment_terms: '60 يوم',
          total_orders: 18,
          total_amount: 950000,
          last_order_date: '2024-05-10',
          notes: 'يحتاج متابعة في التوريد',
          is_active: true,
        },
        {
          id: 4,
          name: 'مورد قطع الغيار الأوروبية',
          contact_person: 'عبد الرحمن قاسم',
          phone: '038-777888',
          email: '<EMAIL>',
          address: 'الميناء التجاري',
          city: 'عنابة',
          country: 'الجزائر',
          rating: 5,
          lead_time_days: 7,
          payment_terms: '30 يوم',
          total_orders: 28,
          total_amount: 3200000,
          last_order_date: '2024-05-28',
          notes: 'قطع غيار أصلية، جودة ممتازة',
          is_active: true,
        },
        {
          id: 5,
          name: 'شركة النجمة للتوريدات',
          contact_person: 'خديجة بن علي',
          phone: '036-444555',
          email: '<EMAIL>',
          address: 'الحي الصناعي',
          city: 'سطيف',
          country: 'الجزائر',
          rating: 2,
          lead_time_days: 20,
          payment_terms: '15 يوم',
          total_orders: 8,
          total_amount: 320000,
          last_order_date: '2024-04-15',
          notes: 'تأخير في التوريد',
          is_active: false,
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuppliers(mockSuppliers);
    } catch (error) {
      console.error('Error loading suppliers:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedSupplier(null);
    setDialogOpen(true);
  };

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      try {
        // هنا سيتم حذف المورد من قاعدة البيانات
        setSuppliers(prev => prev.filter(supplier => supplier.id !== id));
        setSnackbar({ open: true, message: 'تم حذف المورد بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف المورد', severity: 'error' });
      }
    }
  };

  const handleSave = async (supplierData: Partial<Supplier>) => {
    try {
      if (selectedSupplier) {
        // تحديث مورد موجود
        setSuppliers(prev => prev.map(supplier =>
          supplier.id === selectedSupplier.id ? { ...supplier, ...supplierData } : supplier
        ));
        setSnackbar({ open: true, message: 'تم تحديث المورد بنجاح', severity: 'success' });
      } else {
        // إضافة مورد جديد
        const newSupplier: Supplier = {
          id: Date.now(),
          total_orders: 0,
          total_amount: 0,
          last_order_date: new Date().toISOString().split('T')[0],
          ...supplierData as Supplier,
        };
        setSuppliers(prev => [...prev, newSupplier]);
        setSnackbar({ open: true, message: 'تم إضافة المورد بنجاح', severity: 'success' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ المورد', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.phone.includes(searchTerm);
    const matchesRating = !ratingFilter || supplier.rating >= Number(ratingFilter);
    return matchesSearch && matchesRating;
  });

  // حساب الإحصائيات
  const totalSuppliers = suppliers.length;
  const activeSuppliers = suppliers.filter(supplier => supplier.is_active).length;
  const totalOrders = suppliers.reduce((sum, supplier) => sum + supplier.total_amount, 0);
  const averageRating = suppliers.reduce((sum, supplier) => sum + supplier.rating, 0) / suppliers.length;

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('suppliers')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalSuppliers}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الموردين
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {activeSuppliers}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              موردين نشطين
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {totalOrders.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الطلبات (دج)
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
              <Typography variant="h4" sx={{ color: '#8b5cf6', fontWeight: 'bold', mr: 1 }}>
                {averageRating.toFixed(1)}
              </Typography>
              <StarIcon sx={{ color: '#f59e0b' }} />
            </Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوسط التقييم
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في الموردين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب التقييم</InputLabel>
              <Select
                value={ratingFilter}
                onChange={(e) => setRatingFilter(e.target.value)}
                label="تصفية حسب التقييم"
              >
                <MenuItem value="">جميع التقييمات</MenuItem>
                <MenuItem value="5">5 نجوم</MenuItem>
                <MenuItem value="4">4 نجوم فأكثر</MenuItem>
                <MenuItem value="3">3 نجوم فأكثر</MenuItem>
                <MenuItem value="2">2 نجوم فأكثر</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={5} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addSupplier')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredSuppliers}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* نموذج إضافة/تعديل */}
      <SupplierDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        supplier={selectedSupplier}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Suppliers;
