import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Autocomplete,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Print as PrintIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { dbService } from '../services/database';
import { dbInitService } from '../services/databaseInit';

// تعريف أنواع البيانات
interface SaleInvoice {
  id: number;
  invoice_number: string;
  invoice_date: string;
  customer_name: string;
  customer_id: number;
  total_amount: number;
  discount: number;
  tax: number;
  final_amount: number;
  payment_status: 'paid' | 'unpaid' | 'partial';
  payment_method: string;
  notes: string;
  items: SaleItem[];
}

interface SaleItem {
  id: number;
  part_id: number;
  part_name: string;
  part_number: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
}

interface Part {
  id: number;
  part_number: string;
  part_name: string;
  selling_price: number;
  quantity: number;
}

// مكون نموذج إنشاء فاتورة جديدة
interface InvoiceDialogProps {
  open: boolean;
  onClose: () => void;
  invoice?: SaleInvoice | null;
  onSave: (invoice: Partial<SaleInvoice>) => void;
}

const InvoiceDialog: React.FC<InvoiceDialogProps> = ({ open, onClose, invoice, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<SaleInvoice>>({
    invoice_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    customer_id: 0,
    customer_name: '',
    total_amount: 0,
    discount: 0,
    tax: 0,
    final_amount: 0,
    payment_status: 'unpaid',
    payment_method: 'cash',
    notes: '',
    items: [],
  });

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [parts, setParts] = useState<Part[]>([]);

  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [itemQuantity, setItemQuantity] = useState(1);

  useEffect(() => {
    if (invoice) {
      setFormData(invoice);
    } else {
      const newInvoiceNumber = `INV-${Date.now()}`;
      setFormData(prev => ({
        ...prev,
        invoice_number: newInvoiceNumber,
      }));
    }

    // تحميل العملاء وقطع الغيار عند فتح النموذج
    if (open) {
      loadDialogData();
    }
  }, [invoice, open]);

  const loadDialogData = async () => {
    try {
      const [dbCustomers, dbParts] = await Promise.all([
        dbService.getAllCustomers(),
        dbService.getAllParts()
      ]);

      const customersData: Customer[] = dbCustomers.map(customer => ({
        id: customer.customer_id,
        name: customer.customer_name,
        phone: customer.phone || '',
        email: customer.email || '',
      }));

      const partsData: Part[] = dbParts.map(part => ({
        id: part.part_id,
        part_number: part.part_number,
        part_name: part.part_name,
        selling_price: part.selling_price,
        quantity: part.quantity,
      }));

      setCustomers(customersData);
      setParts(partsData);
    } catch (error) {
      console.error('Error loading dialog data:', error);
    }
  };

  const handleAddItem = () => {
    if (selectedPart && itemQuantity > 0) {
      const newItem: SaleItem = {
        id: Date.now(),
        part_id: selectedPart.id,
        part_name: selectedPart.part_name,
        part_number: selectedPart.part_number,
        quantity: itemQuantity,
        unit_price: selectedPart.selling_price,
        total_price: selectedPart.selling_price * itemQuantity,
      };

      const updatedItems = [...(formData.items || []), newItem];
      const totalAmount = updatedItems.reduce((sum, item) => sum + item.total_price, 0);
      const taxAmount = totalAmount * 0.19; // 19% ضريبة
      const finalAmount = totalAmount + taxAmount - (formData.discount || 0);

      setFormData(prev => ({
        ...prev,
        items: updatedItems,
        total_amount: totalAmount,
        tax: taxAmount,
        final_amount: finalAmount,
      }));

      setSelectedPart(null);
      setItemQuantity(1);
    }
  };

  const handleRemoveItem = (itemId: number) => {
    const updatedItems = (formData.items || []).filter(item => item.id !== itemId);
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.total_price, 0);
    const taxAmount = totalAmount * 0.19;
    const finalAmount = totalAmount + taxAmount - (formData.discount || 0);

    setFormData(prev => ({
      ...prev,
      items: updatedItems,
      total_amount: totalAmount,
      tax: taxAmount,
      final_amount: finalAmount,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {invoice ? 'تعديل فاتورة' : 'فاتورة جديدة'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="رقم الفاتورة"
              value={formData.invoice_number || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_number: e.target.value }))}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="تاريخ الفاتورة"
              type="date"
              value={formData.invoice_date || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
              required
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={customers}
              getOptionLabel={(option) => option.name}
              value={customers.find(c => c.id === formData.customer_id) || null}
              onChange={(_, newValue) => {
                setFormData(prev => ({
                  ...prev,
                  customer_id: newValue?.id || 0,
                  customer_name: newValue?.name || '',
                }));
              }}
              renderInput={(params) => (
                <TextField {...params} label="العميل" required />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>طريقة الدفع</InputLabel>
              <Select
                value={formData.payment_method || 'cash'}
                onChange={(e) => setFormData(prev => ({ ...prev, payment_method: e.target.value }))}
                label="طريقة الدفع"
              >
                <MenuItem value="cash">نقداً</MenuItem>
                <MenuItem value="card">بطاقة ائتمان</MenuItem>
                <MenuItem value="bank_transfer">تحويل بنكي</MenuItem>
                <MenuItem value="check">شيك</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* إضافة قطع الغيار */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
              إضافة قطع الغيار
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <Autocomplete
                  options={parts}
                  getOptionLabel={(option) => `${option.part_number} - ${option.part_name}`}
                  value={selectedPart}
                  onChange={(_, newValue) => setSelectedPart(newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="اختر قطعة الغيار" />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="الكمية"
                  type="number"
                  value={itemQuantity}
                  onChange={(e) => setItemQuantity(Number(e.target.value))}
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="السعر"
                  value={selectedPart?.selling_price || 0}
                  disabled
                  InputProps={{
                    endAdornment: <Typography variant="body2">دج</Typography>,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleAddItem}
                  disabled={!selectedPart}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  إضافة
                </Button>
              </Grid>
            </Grid>
          </Grid>

          {/* جدول القطع المضافة */}
          {formData.items && formData.items.length > 0 && (
            <Grid item xs={12}>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>رقم القطعة</TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>اسم القطعة</TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>الكمية</TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>السعر</TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>الإجمالي</TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>العمليات</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {formData.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>{item.part_number}</TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>{item.part_name}</TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>{item.quantity}</TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>{item.unit_price.toLocaleString()} دج</TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>{item.total_price.toLocaleString()} دج</TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveItem(item.id)}
                            sx={{ color: '#ef4444' }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          )}

          {/* ملخص الفاتورة */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="الخصم"
              type="number"
              value={formData.discount || 0}
              onChange={(e) => {
                const discount = Number(e.target.value);
                const finalAmount = (formData.total_amount || 0) + (formData.tax || 0) - discount;
                setFormData(prev => ({ ...prev, discount, final_amount: finalAmount }));
              }}
              InputProps={{
                endAdornment: <Typography variant="body2">دج</Typography>,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>حالة الدفع</InputLabel>
              <Select
                value={formData.payment_status || 'unpaid'}
                onChange={(e) => setFormData(prev => ({ ...prev, payment_status: e.target.value as any }))}
                label="حالة الدفع"
              >
                <MenuItem value="paid">مدفوع</MenuItem>
                <MenuItem value="unpaid">غير مدفوع</MenuItem>
                <MenuItem value="partial">جزئي</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Paper sx={{ p: 2, backgroundColor: '#f8fafc' }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    المجموع الفرعي: {(formData.total_amount || 0).toLocaleString()} دج
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    الضريبة (19%): {(formData.tax || 0).toLocaleString()} دج
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                    الخصم: {(formData.discount || 0).toLocaleString()} دج
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                    المجموع النهائي: {(formData.final_amount || 0).toLocaleString()} دج
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات"
              multiline
              rows={3}
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          إلغاء
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          حفظ
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Sales: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const [invoices, setInvoices] = useState<SaleInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<SaleInvoice | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'invoice_number',
      headerName: 'رقم الفاتورة',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'invoice_date',
      headerName: 'التاريخ',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {new Date(params.value).toLocaleDateString('ar-DZ')}
        </Typography>
      ),
    },
    {
      field: 'customer_name',
      headerName: 'العميل',
      width: 180,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'final_amount',
      headerName: 'المبلغ النهائي',
      width: 150,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'payment_status',
      headerName: 'حالة الدفع',
      width: 120,
      renderCell: (params) => {
        const getStatusColor = (status: string) => {
          switch (status) {
            case 'paid': return 'success';
            case 'unpaid': return 'error';
            case 'partial': return 'warning';
            default: return 'default';
          }
        };

        const getStatusText = (status: string) => {
          switch (status) {
            case 'paid': return 'مدفوع';
            case 'unpaid': return 'غير مدفوع';
            case 'partial': return 'جزئي';
            default: return status;
          }
        };

        return (
          <Chip
            label={getStatusText(params.value)}
            size="small"
            color={getStatusColor(params.value) as any}
          />
        );
      },
    },
    {
      field: 'payment_method',
      headerName: 'طريقة الدفع',
      width: 120,
      renderCell: (params) => {
        const getMethodText = (method: string) => {
          switch (method) {
            case 'cash': return 'نقداً';
            case 'card': return 'بطاقة';
            case 'bank_transfer': return 'تحويل';
            case 'check': return 'شيك';
            default: return method;
          }
        };

        return (
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {getMethodText(params.value)}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 150,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row)}
            sx={{ color: '#3b82f6' }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handlePrint(params.row)}
            sx={{ color: '#10b981' }}
          >
            <PrintIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            sx={{ color: '#ef4444' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadInvoices();
  }, []);

  // فتح النموذج تلقائياً عند القدوم من لوحة التحكم
  useEffect(() => {
    if (location.state?.openDialog) {
      setDialogOpen(true);
      // مسح الـ state لتجنب فتح النموذج مرة أخرى عند إعادة التحميل
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const loadInvoices = async () => {
    try {
      setLoading(true);

      // تهيئة قاعدة البيانات
      await dbInitService.initializeDatabase();

      // تحميل الفواتير من قاعدة البيانات
      const dbInvoices = await dbService.getAllSalesInvoices();
      const customers = await dbService.getAllCustomers();

      // إنشاء خريطة للعملاء للوصول السريع
      const customerMap = new Map(customers.map(customer => [customer.customer_id, customer.customer_name]));

      // تحويل البيانات إلى التنسيق المطلوب
      const invoices: SaleInvoice[] = dbInvoices.map(invoice => ({
        id: invoice.invoice_id,
        invoice_number: invoice.invoice_number,
        invoice_date: invoice.invoice_date,
        customer_name: customerMap.get(invoice.customer_id) || 'عميل غير معروف',
        customer_id: invoice.customer_id,
        total_amount: invoice.total_amount,
        discount: invoice.discount_amount,
        tax: invoice.tax_amount,
        final_amount: invoice.final_amount,
        payment_status: invoice.payment_status,
        payment_method: 'cash', // افتراضي
        notes: invoice.notes || '',
        items: [], // سيتم تحميلها لاحقاً عند الحاجة
      }));

      setInvoices(invoices);

      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error loading invoices:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedInvoice(null);
    setDialogOpen(true);
  };

  const handleEdit = (invoice: SaleInvoice) => {
    setSelectedInvoice(invoice);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
      try {
        // هنا سيتم حذف الفاتورة من قاعدة البيانات
        setInvoices(prev => prev.filter(invoice => invoice.id !== id));
        setSnackbar({ open: true, message: 'تم حذف الفاتورة بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف الفاتورة', severity: 'error' });
      }
    }
  };

  const handlePrint = (invoice: SaleInvoice) => {
    // هنا سيتم تنفيذ منطق طباعة الفاتورة
    console.log('Printing invoice:', invoice);
    setSnackbar({ open: true, message: 'سيتم تنفيذ الطباعة قريباً', severity: 'success' });
  };

  const handleSave = async (invoiceData: Partial<SaleInvoice>) => {
    try {
      if (selectedInvoice) {
        // تحديث فاتورة موجودة
        const updateData = {
          customer_id: invoiceData.customer_id,
          invoice_number: invoiceData.invoice_number,
          invoice_date: invoiceData.invoice_date,
          total_amount: invoiceData.total_amount,
          discount_amount: invoiceData.discount,
          tax_amount: invoiceData.tax,
          final_amount: invoiceData.final_amount,
          payment_status: invoiceData.payment_status,
          notes: invoiceData.notes,
        };

        await dbService.updateSalesInvoice(selectedInvoice.id, updateData);

        setInvoices(prev => prev.map(invoice =>
          invoice.id === selectedInvoice.id ? { ...invoice, ...invoiceData } : invoice
        ));
        setSnackbar({ open: true, message: 'تم تحديث الفاتورة بنجاح', severity: 'success' });
      } else {
        // إضافة فاتورة جديدة
        const newInvoiceData = {
          customer_id: invoiceData.customer_id || 0,
          invoice_number: invoiceData.invoice_number || '',
          invoice_date: invoiceData.invoice_date || new Date().toISOString().split('T')[0],
          total_amount: invoiceData.total_amount || 0,
          discount_amount: invoiceData.discount || 0,
          tax_amount: invoiceData.tax || 0,
          final_amount: invoiceData.final_amount || 0,
          paid_amount: invoiceData.payment_status === 'paid' ? (invoiceData.final_amount || 0) : 0,
          payment_status: invoiceData.payment_status || 'unpaid',
          sales_channel: 'in_store' as const,
          notes: invoiceData.notes,
          user_id: 1, // افتراضي
        };

        const newId = await dbService.createSalesInvoice(newInvoiceData);

        const newInvoice: SaleInvoice = {
          ...invoiceData as SaleInvoice,
          id: newId,
        };
        setInvoices(prev => [...prev, newInvoice]);
        setSnackbar({ open: true, message: 'تم إضافة الفاتورة بنجاح', severity: 'success' });
      }
      setDialogOpen(false);
      setSelectedInvoice(null);
    } catch (error) {
      console.error('Error saving invoice:', error);
      setSnackbar({ open: true, message: 'خطأ في حفظ الفاتورة', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.customer_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || invoice.payment_status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // حساب الإحصائيات
  const totalSales = invoices.reduce((sum, invoice) => sum + invoice.final_amount, 0);
  const paidInvoices = invoices.filter(invoice => invoice.payment_status === 'paid').length;
  const unpaidInvoices = invoices.filter(invoice => invoice.payment_status === 'unpaid').length;

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('sales')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalSales.toLocaleString()}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي المبيعات (دج)
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {paidInvoices}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              فواتير مدفوعة
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
              {unpaidInvoices}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              فواتير غير مدفوعة
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="تصفية حسب الحالة"
              >
                <MenuItem value="">جميع الحالات</MenuItem>
                <MenuItem value="paid">مدفوع</MenuItem>
                <MenuItem value="unpaid">غير مدفوع</MenuItem>
                <MenuItem value="partial">جزئي</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={5} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('newSale')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredInvoices}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* نموذج إضافة/تعديل */}
      <InvoiceDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        invoice={selectedInvoice}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Sales;
