import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Tooltip,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Send as SendIcon,
  Check as CheckIcon,
  Cancel as CancelIcon,
  LocalShipping as ShippingIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  ShoppingCart as CartIcon,
  Business as SupplierIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useNotifications } from '../contexts/NotificationContext';

// تعريف أنواع البيانات
interface PurchaseOrder {
  id: number;
  order_number: string;
  supplier_id: number;
  supplier_name: string;
  supplier_phone: string;
  supplier_email: string;
  status: 'draft' | 'sent' | 'confirmed' | 'received' | 'cancelled';
  order_date: string;
  expected_delivery_date: string;
  actual_delivery_date?: string;
  total_amount: number;
  items_count: number;
  tax_amount: number;
  discount_amount: number;
  final_amount: number;
  notes: string;
  terms_conditions: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  items?: PurchaseOrderItem[];
}

interface PurchaseOrderItem {
  id: number;
  purchase_order_id: number;
  part_id: number;
  part_number: string;
  part_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  received_quantity?: number;
  notes: string;
}

interface Supplier {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  is_active: boolean;
}

const PurchaseOrders: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { addNotification } = useNotifications();
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [supplierFilter, setSupplierFilter] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'order_info',
      headerName: 'معلومات الطلبية',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CartIcon sx={{ mr: 1, color: '#64748b' }} />
          <Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', fontSize: '0.875rem' }}>
              {params.row.order_number}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontSize: '0.75rem', color: '#64748b' }}>
              {new Date(params.row.order_date).toLocaleDateString('ar-DZ')}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'supplier_info',
      headerName: 'المورد',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SupplierIcon sx={{ mr: 1, color: '#64748b' }} />
          <Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', fontSize: '0.875rem' }}>
              {params.row.supplier_name}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontSize: '0.75rem', color: '#64748b' }}>
              {params.row.supplier_phone}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params) => {
        const getStatusConfig = (status: string) => {
          switch (status) {
            case 'draft':
              return { label: 'مسودة', color: 'default' as const, icon: <EditIcon /> };
            case 'sent':
              return { label: 'مرسلة', color: 'info' as const, icon: <SendIcon /> };
            case 'confirmed':
              return { label: 'مؤكدة', color: 'warning' as const, icon: <CheckIcon /> };
            case 'received':
              return { label: 'مستلمة', color: 'success' as const, icon: <ShippingIcon /> };
            case 'cancelled':
              return { label: 'ملغاة', color: 'error' as const, icon: <CancelIcon /> };
            default:
              return { label: status, color: 'default' as const, icon: <EditIcon /> };
          }
        };

        const config = getStatusConfig(params.value);
        return (
          <Chip
            icon={config.icon}
            label={config.label}
            size="small"
            color={config.color}
          />
        );
      },
    },
    {
      field: 'items_count',
      headerName: 'عدد الأصناف',
      width: 100,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
          {params.value} صنف
        </Typography>
      ),
    },
    {
      field: 'final_amount',
      headerName: 'إجمالي المبلغ',
      width: 130,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', color: '#3b82f6' }}>
          {params.value.toLocaleString()} دج
        </Typography>
      ),
    },
    {
      field: 'expected_delivery_date',
      headerName: 'التسليم المتوقع',
      width: 130,
      renderCell: (params) => {
        const isOverdue = new Date(params.value) < new Date() && params.row.status !== 'received';
        return (
          <Typography sx={{
            fontFamily: 'Cairo, sans-serif',
            color: isOverdue ? '#ef4444' : '#64748b'
          }}>
            {new Date(params.value).toLocaleDateString('ar-DZ')}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="عرض التفاصيل">
            <IconButton
              size="small"
              onClick={() => handleView(params.row)}
              sx={{ color: '#3b82f6' }}
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل">
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              sx={{ color: '#10b981' }}
              disabled={params.row.status === 'received' || params.row.status === 'cancelled'}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          {params.row.status === 'draft' && (
            <Tooltip title="إرسال">
              <IconButton
                size="small"
                onClick={() => handleSend(params.row)}
                sx={{ color: '#f59e0b' }}
              >
                <SendIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {params.row.status === 'confirmed' && (
            <Tooltip title="تأكيد الاستلام">
              <IconButton
                size="small"
                onClick={() => handleReceive(params.row)}
                sx={{ color: '#10b981' }}
              >
                <ShippingIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="حذف">
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row.id)}
              sx={{ color: '#ef4444' }}
              disabled={params.row.status === 'received'}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadData();
  }, []);

  // فتح النموذج تلقائياً عند القدوم من لوحة التحكم
  useEffect(() => {
    if (location.state?.openDialog) {
      setDialogOpen(true);
      // مسح الـ state لتجنب فتح النموذج مرة أخرى عند إعادة التحميل
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const loadData = async () => {
    try {
      setLoading(true);

      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockSuppliers: Supplier[] = [
        { id: 1, name: 'شركة قطع الغيار المتقدمة', phone: '0123456789', email: '<EMAIL>', address: 'المنطقة الصناعية', city: 'الجزائر', is_active: true },
        { id: 2, name: 'مؤسسة الشاحنات الحديثة', phone: '0987654321', email: '<EMAIL>', address: 'شارع التجارة', city: 'وهران', is_active: true },
        { id: 3, name: 'شركة الإمداد السريع', phone: '0555123456', email: '<EMAIL>', address: 'حي الصناعات', city: 'قسنطينة', is_active: true },
      ];

      const mockPurchaseOrders: PurchaseOrder[] = [
        {
          id: 1,
          order_number: 'PO-2024-001',
          supplier_id: 1,
          supplier_name: 'شركة قطع الغيار المتقدمة',
          supplier_phone: '0123456789',
          supplier_email: '<EMAIL>',
          status: 'confirmed',
          order_date: '2024-05-20',
          expected_delivery_date: '2024-06-05',
          total_amount: 450000,
          items_count: 15,
          tax_amount: 81000,
          discount_amount: 22500,
          final_amount: 508500,
          notes: 'طلبية عاجلة - قطع غيار للمحرك',
          terms_conditions: 'الدفع خلال 30 يوم من التسليم',
          created_by: 'أحمد المدير',
          created_at: '2024-05-20',
          updated_at: '2024-05-22',
        },
        {
          id: 2,
          order_number: 'PO-2024-002',
          supplier_id: 2,
          supplier_name: 'مؤسسة الشاحنات الحديثة',
          supplier_phone: '0987654321',
          supplier_email: '<EMAIL>',
          status: 'sent',
          order_date: '2024-05-25',
          expected_delivery_date: '2024-06-10',
          total_amount: 320000,
          items_count: 8,
          tax_amount: 57600,
          discount_amount: 16000,
          final_amount: 361600,
          notes: 'قطع فرامل وإطارات',
          terms_conditions: 'الدفع عند التسليم',
          created_by: 'فاطمة المشتريات',
          created_at: '2024-05-25',
          updated_at: '2024-05-25',
        },
        {
          id: 3,
          order_number: 'PO-2024-003',
          supplier_id: 3,
          supplier_name: 'شركة الإمداد السريع',
          supplier_phone: '0555123456',
          supplier_email: '<EMAIL>',
          status: 'received',
          order_date: '2024-05-15',
          expected_delivery_date: '2024-05-30',
          actual_delivery_date: '2024-05-28',
          total_amount: 180000,
          items_count: 12,
          tax_amount: 32400,
          discount_amount: 9000,
          final_amount: 203400,
          notes: 'قطع كهربائية وإضاءة',
          terms_conditions: 'ضمان 6 أشهر على جميع القطع',
          created_by: 'محمد المخزن',
          created_at: '2024-05-15',
          updated_at: '2024-05-28',
        },
        {
          id: 4,
          order_number: 'PO-2024-004',
          supplier_id: 1,
          supplier_name: 'شركة قطع الغيار المتقدمة',
          supplier_phone: '0123456789',
          supplier_email: '<EMAIL>',
          status: 'draft',
          order_date: '2024-05-30',
          expected_delivery_date: '2024-06-15',
          total_amount: 275000,
          items_count: 6,
          tax_amount: 49500,
          discount_amount: 13750,
          final_amount: 310750,
          notes: 'مسودة - قطع تكييف',
          terms_conditions: 'يتم التفاوض على الشروط',
          created_by: 'سارة المبيعات',
          created_at: '2024-05-30',
          updated_at: '2024-05-30',
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuppliers(mockSuppliers);
      setPurchaseOrders(mockPurchaseOrders);
    } catch (error) {
      console.error('Error loading data:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedOrder(null);
    setDialogOpen(true);
  };

  const handleEdit = (order: PurchaseOrder) => {
    setSelectedOrder(order);
    setDialogOpen(true);
  };

  const handleView = (order: PurchaseOrder) => {
    setSelectedOrder(order);
    setViewDialogOpen(true);
  };

  const handleSend = async (order: PurchaseOrder) => {
    if (window.confirm(`هل أنت متأكد من إرسال الطلبية ${order.order_number}؟`)) {
      try {
        setPurchaseOrders(prev => prev.map(po =>
          po.id === order.id ? { ...po, status: 'sent' as const, updated_at: new Date().toISOString().split('T')[0] } : po
        ));

        addNotification({
          type: 'success',
          title: 'تم إرسال الطلبية',
          message: `تم إرسال الطلبية ${order.order_number} إلى ${order.supplier_name}`,
          priority: 'medium',
        });

        setSnackbar({ open: true, message: 'تم إرسال الطلبية بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في إرسال الطلبية', severity: 'error' });
      }
    }
  };

  const handleReceive = async (order: PurchaseOrder) => {
    if (window.confirm(`هل أنت متأكد من تأكيد استلام الطلبية ${order.order_number}؟`)) {
      try {
        setPurchaseOrders(prev => prev.map(po =>
          po.id === order.id ? {
            ...po,
            status: 'received' as const,
            actual_delivery_date: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
          } : po
        ));

        addNotification({
          type: 'success',
          title: 'تم استلام الطلبية',
          message: `تم تأكيد استلام الطلبية ${order.order_number} وتحديث المخزون`,
          priority: 'medium',
        });

        setSnackbar({ open: true, message: 'تم تأكيد الاستلام وتحديث المخزون', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في تأكيد الاستلام', severity: 'error' });
      }
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الطلبية؟')) {
      try {
        setPurchaseOrders(prev => prev.filter(po => po.id !== id));
        setSnackbar({ open: true, message: 'تم حذف الطلبية بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف الطلبية', severity: 'error' });
      }
    }
  };

  // تصفية البيانات
  const filteredOrders = purchaseOrders.filter(order => {
    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.supplier_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || order.status === statusFilter;
    const matchesSupplier = !supplierFilter || order.supplier_id.toString() === supplierFilter;
    return matchesSearch && matchesStatus && matchesSupplier;
  });

  // حساب الإحصائيات
  const totalOrders = purchaseOrders.length;
  const draftOrders = purchaseOrders.filter(po => po.status === 'draft').length;
  const pendingOrders = purchaseOrders.filter(po => ['sent', 'confirmed'].includes(po.status)).length;
  const completedOrders = purchaseOrders.filter(po => po.status === 'received').length;
  const totalValue = purchaseOrders.reduce((sum, po) => sum + po.final_amount, 0);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        طلبيات الشراء
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalOrders}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الطلبيات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {draftOrders}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              مسودات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
              {pendingOrders}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              قيد التنفيذ
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {completedOrders}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              مكتملة
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="البحث في الطلبيات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={2.5}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب الحالة</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="تصفية حسب الحالة"
              >
                <MenuItem value="">جميع الحالات</MenuItem>
                <MenuItem value="draft">مسودة</MenuItem>
                <MenuItem value="sent">مرسلة</MenuItem>
                <MenuItem value="confirmed">مؤكدة</MenuItem>
                <MenuItem value="received">مستلمة</MenuItem>
                <MenuItem value="cancelled">ملغاة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2.5}>
            <FormControl fullWidth>
              <InputLabel>تصفية حسب المورد</InputLabel>
              <Select
                value={supplierFilter}
                onChange={(e) => setSupplierFilter(e.target.value)}
                label="تصفية حسب المورد"
              >
                <MenuItem value="">جميع الموردين</MenuItem>
                {suppliers.map((supplier) => (
                  <MenuItem key={supplier.id} value={supplier.id.toString()}>
                    {supplier.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              إضافة طلبية شراء
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* جدول البيانات */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredOrders}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          sx={{
            '& .MuiDataGrid-root': {
              fontFamily: 'Cairo, sans-serif',
            },
          }}
        />
      </Paper>

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PurchaseOrders;
