import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  Autocomplete,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Link as LinkIcon,
  CarRepair as TruckIcon,
  Build as PartIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface TruckModel {
  id: number;
  model_name: string;
  manufacturer: string;
  production_year: number;
}

interface Part {
  id: number;
  part_number: string;
  part_name: string;
  category: string;
}

interface Compatibility {
  id: number;
  part_id: number;
  truck_model_id: number;
  is_compatible: boolean;
  notes: string;
  created_at: string;
  part?: Part;
  truck_model?: TruckModel;
}

// مكون نموذج إضافة توافق
interface CompatibilityDialogProps {
  open: boolean;
  onClose: () => void;
  parts: Part[];
  truckModels: TruckModel[];
  onSave: (compatibility: Partial<Compatibility>) => void;
}

const CompatibilityDialog: React.FC<CompatibilityDialogProps> = ({ open, onClose, parts, truckModels, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Compatibility>>({
    part_id: undefined,
    truck_model_id: undefined,
    is_compatible: true,
    notes: '',
  });

  useEffect(() => {
    if (open) {
      setFormData({
        part_id: undefined,
        truck_model_id: undefined,
        is_compatible: true,
        notes: '',
      });
    }
  }, [open]);

  const handleSubmit = () => {
    if (formData.part_id && formData.truck_model_id) {
      onSave(formData);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {t('addCompatibility')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={parts}
              getOptionLabel={(option) => `${option.part_number} - ${option.part_name}`}
              value={parts.find(p => p.id === formData.part_id) || null}
              onChange={(_, newValue) => {
                setFormData(prev => ({ ...prev, part_id: newValue?.id }));
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="قطعة الغيار"
                  required
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={truckModels}
              getOptionLabel={(option) => `${option.manufacturer} ${option.model_name} (${option.production_year})`}
              value={truckModels.find(m => m.id === formData.truck_model_id) || null}
              onChange={(_, newValue) => {
                setFormData(prev => ({ ...prev, truck_model_id: newValue?.id }));
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="موديل الشاحنة"
                  required
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>حالة التوافق</InputLabel>
              <Select
                value={formData.is_compatible ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_compatible: e.target.value === 1 }))}
                label="حالة التوافق"
              >
                <MenuItem value={1}>متوافق</MenuItem>
                <MenuItem value={0}>غير متوافق</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات"
              multiline
              rows={3}
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// مكون عرض القطع المتوافقة لموديل معين
interface CompatiblePartsProps {
  truckModels: TruckModel[];
  compatibilities: Compatibility[];
  onDelete: (id: number) => void;
}

const CompatibleParts: React.FC<CompatiblePartsProps> = ({ truckModels, compatibilities, onDelete }) => {
  const [selectedModel, setSelectedModel] = useState<number | null>(null);

  const filteredCompatibilities = selectedModel
    ? compatibilities.filter(comp => comp.truck_model_id === selectedModel)
    : [];

  return (
    <Box>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>اختر موديل الشاحنة</InputLabel>
            <Select
              value={selectedModel || ''}
              onChange={(e) => setSelectedModel(Number(e.target.value) || null)}
              label="اختر موديل الشاحنة"
            >
              <MenuItem value="">-- اختر موديل --</MenuItem>
              {truckModels.map(model => (
                <MenuItem key={model.id} value={model.id}>
                  {model.manufacturer} {model.model_name} ({model.production_year})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {selectedModel && (
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            القطع المتوافقة ({filteredCompatibilities.length})
          </Typography>
          <List>
            {filteredCompatibilities.map(comp => (
              <ListItem key={comp.id} divider>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PartIcon sx={{ color: '#64748b' }} />
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                        {comp.part?.part_number} - {comp.part?.part_name}
                      </Typography>
                      <Chip
                        icon={comp.is_compatible ? <CheckIcon /> : <CancelIcon />}
                        label={comp.is_compatible ? 'متوافق' : 'غير متوافق'}
                        size="small"
                        color={comp.is_compatible ? 'success' : 'error'}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
                        الفئة: {comp.part?.category}
                      </Typography>
                      {comp.notes && (
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b', fontSize: '0.875rem' }}>
                          ملاحظات: {comp.notes}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => onDelete(comp.id)}
                    sx={{ color: '#ef4444' }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          {filteredCompatibilities.length === 0 && (
            <Typography sx={{ textAlign: 'center', color: '#64748b', py: 4 }}>
              لا توجد قطع متوافقة مع هذا الموديل
            </Typography>
          )}
        </Paper>
      )}
    </Box>
  );
};

// مكون عرض الموديلات المتوافقة لقطعة معينة
interface CompatibleModelsProps {
  parts: Part[];
  compatibilities: Compatibility[];
  onDelete: (id: number) => void;
}

const CompatibleModels: React.FC<CompatibleModelsProps> = ({ parts, compatibilities, onDelete }) => {
  const [selectedPart, setSelectedPart] = useState<number | null>(null);

  const filteredCompatibilities = selectedPart
    ? compatibilities.filter(comp => comp.part_id === selectedPart)
    : [];

  return (
    <Box>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>اختر قطعة الغيار</InputLabel>
            <Select
              value={selectedPart || ''}
              onChange={(e) => setSelectedPart(Number(e.target.value) || null)}
              label="اختر قطعة الغيار"
            >
              <MenuItem value="">-- اختر قطعة --</MenuItem>
              {parts.map(part => (
                <MenuItem key={part.id} value={part.id}>
                  {part.part_number} - {part.part_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {selectedPart && (
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            الموديلات المتوافقة ({filteredCompatibilities.length})
          </Typography>
          <List>
            {filteredCompatibilities.map(comp => (
              <ListItem key={comp.id} divider>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TruckIcon sx={{ color: '#64748b' }} />
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                        {comp.truck_model?.manufacturer} {comp.truck_model?.model_name} ({comp.truck_model?.production_year})
                      </Typography>
                      <Chip
                        icon={comp.is_compatible ? <CheckIcon /> : <CancelIcon />}
                        label={comp.is_compatible ? 'متوافق' : 'غير متوافق'}
                        size="small"
                        color={comp.is_compatible ? 'success' : 'error'}
                      />
                    </Box>
                  }
                  secondary={
                    comp.notes && (
                      <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b', fontSize: '0.875rem' }}>
                        ملاحظات: {comp.notes}
                      </Typography>
                    )
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => onDelete(comp.id)}
                    sx={{ color: '#ef4444' }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          {filteredCompatibilities.length === 0 && (
            <Typography sx={{ textAlign: 'center', color: '#64748b', py: 4 }}>
              لا توجد موديلات متوافقة مع هذه القطعة
            </Typography>
          )}
        </Paper>
      )}
    </Box>
  );
};

const Compatibility: React.FC = () => {
  const { t } = useTranslation();
  const [compatibilities, setCompatibilities] = useState<Compatibility[]>([]);
  const [parts, setParts] = useState<Part[]>([]);
  const [truckModels, setTruckModels] = useState<TruckModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحديد أعمدة الجدول
  const columns: GridColDef[] = [
    {
      field: 'part_info',
      headerName: 'قطعة الغيار',
      width: 250,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PartIcon sx={{ mr: 1, color: '#64748b' }} />
          <Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', fontSize: '0.875rem' }}>
              {params.row.part?.part_number}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontSize: '0.75rem', color: '#64748b' }}>
              {params.row.part?.part_name}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'truck_model_info',
      headerName: 'موديل الشاحنة',
      width: 250,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TruckIcon sx={{ mr: 1, color: '#64748b' }} />
          <Box>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold', fontSize: '0.875rem' }}>
              {params.row.truck_model?.manufacturer} {params.row.truck_model?.model_name}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontSize: '0.75rem', color: '#64748b' }}>
              سنة الإنتاج: {params.row.truck_model?.production_year}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: 'الفئة',
      width: 120,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.row.part?.category}
        </Typography>
      ),
    },
    {
      field: 'is_compatible',
      headerName: 'حالة التوافق',
      width: 130,
      renderCell: (params) => (
        <Chip
          icon={params.value ? <CheckIcon /> : <CancelIcon />}
          label={params.value ? 'متوافق' : 'غير متوافق'}
          size="small"
          color={params.value ? 'success' : 'error'}
        />
      ),
    },
    {
      field: 'notes',
      headerName: 'ملاحظات',
      width: 200,
      renderCell: (params) => (
        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {params.value || '-'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'العمليات',
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <IconButton
          size="small"
          onClick={() => handleDelete(params.row.id)}
          sx={{ color: '#ef4444' }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      ),
    },
  ];

  // تحميل البيانات
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockParts: Part[] = [
        { id: 1, part_number: 'ENG001', part_name: 'فلتر هواء', category: 'محرك' },
        { id: 2, part_number: 'BRK001', part_name: 'قرص فرامل أمامي', category: 'فرامل' },
        { id: 3, part_number: 'ENG002', part_name: 'فلتر زيت', category: 'محرك' },
        { id: 4, part_number: 'ELC001', part_name: 'بطارية 12V', category: 'كهرباء' },
        { id: 5, part_number: 'TIR001', part_name: 'إطار 295/80R22.5', category: 'إطارات' },
      ];

      const mockTruckModels: TruckModel[] = [
        { id: 1, model_name: 'Actros 1845', manufacturer: 'Mercedes-Benz', production_year: 2022 },
        { id: 2, model_name: 'FH16 750', manufacturer: 'Volvo', production_year: 2023 },
        { id: 3, model_name: 'R 450', manufacturer: 'Scania', production_year: 2021 },
        { id: 4, model_name: 'Stralis XP 570', manufacturer: 'Iveco', production_year: 2020 },
        { id: 5, model_name: 'CF 440', manufacturer: 'DAF', production_year: 2019 },
      ];

      const mockCompatibilities: Compatibility[] = [
        {
          id: 1,
          part_id: 1,
          truck_model_id: 1,
          is_compatible: true,
          notes: 'توافق كامل',
          created_at: '2024-05-20',
          part: mockParts[0],
          truck_model: mockTruckModels[0],
        },
        {
          id: 2,
          part_id: 1,
          truck_model_id: 2,
          is_compatible: true,
          notes: '',
          created_at: '2024-05-19',
          part: mockParts[0],
          truck_model: mockTruckModels[1],
        },
        {
          id: 3,
          part_id: 2,
          truck_model_id: 1,
          is_compatible: true,
          notes: 'يحتاج تعديل بسيط',
          created_at: '2024-05-18',
          part: mockParts[1],
          truck_model: mockTruckModels[0],
        },
        {
          id: 4,
          part_id: 3,
          truck_model_id: 3,
          is_compatible: false,
          notes: 'غير متوافق - حجم مختلف',
          created_at: '2024-05-17',
          part: mockParts[2],
          truck_model: mockTruckModels[2],
        },
        {
          id: 5,
          part_id: 4,
          truck_model_id: 4,
          is_compatible: true,
          notes: 'متوافق مع جميع الموديلات',
          created_at: '2024-05-16',
          part: mockParts[3],
          truck_model: mockTruckModels[3],
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setParts(mockParts);
      setTruckModels(mockTruckModels);
      setCompatibilities(mockCompatibilities);
    } catch (error) {
      console.error('Error loading data:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا التوافق؟')) {
      try {
        setCompatibilities(prev => prev.filter(comp => comp.id !== id));
        setSnackbar({ open: true, message: 'تم حذف التوافق بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف التوافق', severity: 'error' });
      }
    }
  };

  const handleSave = async (compatibilityData: Partial<Compatibility>) => {
    try {
      // التحقق من عدم وجود توافق مكرر
      const exists = compatibilities.some(comp =>
        comp.part_id === compatibilityData.part_id &&
        comp.truck_model_id === compatibilityData.truck_model_id
      );

      if (exists) {
        setSnackbar({ open: true, message: 'هذا التوافق موجود بالفعل', severity: 'error' });
        return;
      }

      const part = parts.find(p => p.id === compatibilityData.part_id);
      const truckModel = truckModels.find(m => m.id === compatibilityData.truck_model_id);

      const newCompatibility: Compatibility = {
        id: Date.now(),
        created_at: new Date().toISOString().split('T')[0],
        part,
        truck_model: truckModel,
        ...compatibilityData as Compatibility,
      };

      setCompatibilities(prev => [...prev, newCompatibility]);
      setSnackbar({ open: true, message: 'تم إضافة التوافق بنجاح', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ التوافق', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filteredCompatibilities = compatibilities.filter(comp => {
    const searchLower = searchTerm.toLowerCase();
    return (
      comp.part?.part_number.toLowerCase().includes(searchLower) ||
      comp.part?.part_name.toLowerCase().includes(searchLower) ||
      comp.truck_model?.model_name.toLowerCase().includes(searchLower) ||
      comp.truck_model?.manufacturer.toLowerCase().includes(searchLower)
    );
  });

  // حساب الإحصائيات
  const totalCompatibilities = compatibilities.length;
  const compatibleCount = compatibilities.filter(comp => comp.is_compatible).length;
  const incompatibleCount = compatibilities.filter(comp => !comp.is_compatible).length;
  const uniqueParts = new Set(compatibilities.map(comp => comp.part_id)).size;

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('compatibility')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalCompatibilities}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي التوافقات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {compatibleCount}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوافق
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#ef4444', fontWeight: 'bold' }}>
              {incompatibleCount}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              غير متوافق
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#8b5cf6', fontWeight: 'bold' }}>
              {uniqueParts}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              قطع مختلفة
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="البحث في التوافقات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addCompatibility')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* تبويبات العرض */}
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 500,
            },
          }}
        >
          <Tab
            icon={<LinkIcon />}
            label="جميع التوافقات"
            iconPosition="start"
          />
          <Tab
            icon={<PartIcon />}
            label="القطع المتوافقة"
            iconPosition="start"
          />
          <Tab
            icon={<TruckIcon />}
            label="الموديلات المتوافقة"
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && (
            <Paper sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={filteredCompatibilities}
                columns={columns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                initialState={{
                  pagination: {
                    paginationModel: { page: 0, pageSize: 10 },
                  },
                }}
                sx={{
                  '& .MuiDataGrid-root': {
                    fontFamily: 'Cairo, sans-serif',
                  },
                }}
              />
            </Paper>
          )}
          {tabValue === 1 && (
            <CompatibleParts
              truckModels={truckModels}
              compatibilities={compatibilities}
              onDelete={handleDelete}
            />
          )}
          {tabValue === 2 && (
            <CompatibleModels
              parts={parts}
              compatibilities={compatibilities}
              onDelete={handleDelete}
            />
          )}
        </Box>
      </Paper>

      {/* نموذج إضافة توافق */}
      <CompatibilityDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        parts={parts}
        truckModels={truckModels}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Compatibility;