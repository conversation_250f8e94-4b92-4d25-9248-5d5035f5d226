import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
} from '@mui/material';
import {
  Assessment as ReportsIcon,
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  LocalShipping as SuppliersIcon,
  AttachMoney as MoneyIcon,
  DateRange as DateIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { useTranslation } from 'react-i18next';

// بيانات وهمية للرسوم البيانية
const salesData = [
  { month: 'يناير', sales: 450000, profit: 85000 },
  { month: 'فبراير', sales: 520000, profit: 98000 },
  { month: 'مارس', sales: 480000, profit: 89000 },
  { month: 'أبريل', sales: 610000, profit: 115000 },
  { month: 'مايو', sales: 580000, profit: 108000 },
  { month: 'يونيو', sales: 650000, profit: 125000 },
];

const categoryData = [
  { name: 'محرك', value: 35, amount: 1250000 },
  { name: 'فرامل', value: 25, amount: 890000 },
  { name: 'إطارات', value: 20, amount: 720000 },
  { name: 'كهرباء', value: 15, amount: 540000 },
  { name: 'أخرى', value: 5, amount: 180000 },
];

const inventoryData = [
  { category: 'محرك', inStock: 145, lowStock: 8, outOfStock: 2 },
  { category: 'فرامل', inStock: 89, lowStock: 5, outOfStock: 1 },
  { category: 'إطارات', inStock: 67, lowStock: 3, outOfStock: 0 },
  { category: 'كهرباء', inStock: 123, lowStock: 7, outOfStock: 2 },
  { category: 'تكييف', inStock: 45, lowStock: 2, outOfStock: 1 },
];

const customerData = [
  { type: 'فرد', count: 45, percentage: 50 },
  { type: 'شركة', count: 25, percentage: 28 },
  { type: 'ورشة', count: 15, percentage: 17 },
  { type: 'مالك أسطول', count: 5, percentage: 5 },
];

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

// مكون تقرير المبيعات
const SalesReport: React.FC = () => {
  return (
    <Grid container spacing={3}>
      {/* بطاقات الإحصائيات */}
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 40, color: '#3b82f6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#3b82f6' }}>
              3.29M
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي المبيعات (دج)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <MoneyIcon sx={{ fontSize: 40, color: '#10b981', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#10b981' }}>
              620K
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الأرباح (دج)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <ReportsIcon sx={{ fontSize: 40, color: '#f59e0b', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f59e0b' }}>
              156
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              عدد الفواتير
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <DateIcon sx={{ fontSize: 40, color: '#8b5cf6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#8b5cf6' }}>
              21K
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوسط الفاتورة (دج)
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* رسم بياني للمبيعات الشهرية */}
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            المبيعات والأرباح الشهرية
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={salesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => `${Number(value).toLocaleString()} دج`} />
              <Legend />
              <Area
                type="monotone"
                dataKey="sales"
                stackId="1"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.6}
                name="المبيعات"
              />
              <Area
                type="monotone"
                dataKey="profit"
                stackId="2"
                stroke="#10b981"
                fill="#10b981"
                fillOpacity={0.6}
                name="الأرباح"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>

      {/* رسم دائري للفئات */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            المبيعات حسب الفئة
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `${value}%`} />
            </PieChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );
};

// مكون تقرير المخزون
const InventoryReport: React.FC = () => {
  return (
    <Grid container spacing={3}>
      {/* إحصائيات المخزون */}
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <InventoryIcon sx={{ fontSize: 40, color: '#3b82f6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#3b82f6' }}>
              1,250
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي القطع
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 40, color: '#10b981', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#10b981' }}>
              469
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوفر
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <ReportsIcon sx={{ fontSize: 40, color: '#f59e0b', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f59e0b' }}>
              25
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              مخزون منخفض
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <MoneyIcon sx={{ fontSize: 40, color: '#ef4444', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ef4444' }}>
              6
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              نفد المخزون
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* رسم بياني لحالة المخزون */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            حالة المخزون حسب الفئة
          </Typography>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={inventoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="inStock" fill="#10b981" name="متوفر" />
              <Bar dataKey="lowStock" fill="#f59e0b" name="مخزون منخفض" />
              <Bar dataKey="outOfStock" fill="#ef4444" name="نفد المخزون" />
            </BarChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );
};

// مكون تقرير العملاء
const CustomersReport: React.FC = () => {
  return (
    <Grid container spacing={3}>
      {/* إحصائيات العملاء */}
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <PeopleIcon sx={{ fontSize: 40, color: '#3b82f6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#3b82f6' }}>
              90
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي العملاء
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 40, color: '#10b981', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#10b981' }}>
              78
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              عملاء نشطون
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <MoneyIcon sx={{ fontSize: 40, color: '#f59e0b', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f59e0b' }}>
              36.5K
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوسط الشراء (دج)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <DateIcon sx={{ fontSize: 40, color: '#8b5cf6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#8b5cf6' }}>
              12
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              عملاء جدد
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* رسم دائري لأنواع العملاء */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            توزيع العملاء حسب النوع
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={customerData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percentage }) => `${name} ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {customerData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>

      {/* جدول أفضل العملاء */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            أفضل العملاء
          </Typography>
          <Box>
            {[
              { name: 'شركة النقل السريع', amount: 450000 },
              { name: 'محمد بن علي', amount: 320000 },
              { name: 'أحمد محمد', amount: 125000 },
              { name: 'ورشة الأمين', amount: 89000 },
              { name: 'فاطمة الزهراء', amount: 25000 },
            ].map((customer, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 1,
                  borderBottom: index < 4 ? '1px solid #e2e8f0' : 'none',
                }}
              >
                <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                  {customer.name}
                </Typography>
                <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                  {customer.amount.toLocaleString()} دج
                </Typography>
              </Box>
            ))}
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

// مكون تقرير الموردين
const SuppliersReport: React.FC = () => {
  const supplierPerformance = [
    { name: 'قطع الغيار المتحدة', rating: 5, orders: 45, amount: 2500000 },
    { name: 'مؤسسة الشرق', rating: 4, orders: 32, amount: 1800000 },
    { name: 'قطع الغيار الأوروبية', rating: 5, orders: 28, amount: 3200000 },
    { name: 'شركة الأطلس', rating: 3, orders: 18, amount: 950000 },
    { name: 'شركة النجمة', rating: 2, orders: 8, amount: 320000 },
  ];

  return (
    <Grid container spacing={3}>
      {/* إحصائيات الموردين */}
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <SuppliersIcon sx={{ fontSize: 40, color: '#3b82f6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#3b82f6' }}>
              15
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الموردين
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 40, color: '#10b981', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#10b981' }}>
              12
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              موردين نشطين
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <MoneyIcon sx={{ fontSize: 40, color: '#f59e0b', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f59e0b' }}>
              8.77M
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الطلبات (دج)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <ReportsIcon sx={{ fontSize: 40, color: '#8b5cf6', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#8b5cf6' }}>
              4.2
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              متوسط التقييم
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* رسم بياني لأداء الموردين */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Cairo, sans-serif' }}>
            أداء الموردين
          </Typography>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={supplierPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip formatter={(value, name) => {
                if (name === 'amount') return `${Number(value).toLocaleString()} دج`;
                return value;
              }} />
              <Legend />
              <Bar yAxisId="left" dataKey="orders" fill="#3b82f6" name="عدد الطلبات" />
              <Bar yAxisId="right" dataKey="amount" fill="#10b981" name="قيمة الطلبات" />
            </BarChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );
};

const Reports: React.FC = () => {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState('thisMonth');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('reports')}
      </Typography>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>الفترة الزمنية</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                label="الفترة الزمنية"
              >
                <MenuItem value="today">اليوم</MenuItem>
                <MenuItem value="thisWeek">هذا الأسبوع</MenuItem>
                <MenuItem value="thisMonth">هذا الشهر</MenuItem>
                <MenuItem value="thisQuarter">هذا الربع</MenuItem>
                <MenuItem value="thisYear">هذا العام</MenuItem>
                <MenuItem value="custom">فترة مخصصة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={8} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="outlined"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              تصدير PDF
            </Button>
            <Button
              variant="outlined"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              تصدير Excel
            </Button>
            <Button
              variant="contained"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              طباعة
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* تبويبات التقارير */}
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif',
              fontWeight: 500,
            },
          }}
        >
          <Tab
            icon={<TrendingUpIcon />}
            label="تقرير المبيعات"
            iconPosition="start"
          />
          <Tab
            icon={<InventoryIcon />}
            label="تقرير المخزون"
            iconPosition="start"
          />
          <Tab
            icon={<PeopleIcon />}
            label="تقرير العملاء"
            iconPosition="start"
          />
          <Tab
            icon={<SuppliersIcon />}
            label="تقرير الموردين"
            iconPosition="start"
          />
        </Tabs>

        {/* محتوى التبويبات */}
        <Box sx={{ p: 3 }}>
          {tabValue === 0 && <SalesReport />}
          {tabValue === 1 && <InventoryReport />}
          {tabValue === 2 && <CustomersReport />}
          {tabValue === 3 && <SuppliersReport />}
        </Box>
      </Paper>
    </Box>
  );
};

export default Reports;