import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Card,
  CardContent,
  CardMedia,
  Avatar,

} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Category as CategoryIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// تعريف أنواع البيانات
interface Category {
  id: number;
  name: string;
  parent_id: number | null;
  description: string;
  image_url: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  children?: Category[];
  parts_count?: number;
}

// مكون نموذج إضافة/تعديل فئة
interface CategoryDialogProps {
  open: boolean;
  onClose: () => void;
  category?: Category | null;
  categories: Category[];
  onSave: (category: Partial<Category>) => void;
}

const CategoryDialog: React.FC<CategoryDialogProps> = ({ open, onClose, category, categories, onSave }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Partial<Category>>({
    name: '',
    parent_id: null,
    description: '',
    image_url: null,
    is_active: true,
  });

  useEffect(() => {
    if (category) {
      setFormData(category);
    } else {
      setFormData({
        name: '',
        parent_id: null,
        description: '',
        image_url: null,
        is_active: true,
      });
    }
  }, [category, open]);

  const handleChange = (field: keyof Category) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  // الحصول على الفئات الرئيسية فقط للاختيار كفئة أب
  const parentCategories = categories.filter(cat => cat.parent_id === null && cat.id !== category?.id);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ fontFamily: 'Cairo, sans-serif' }}>
        {category ? t('editCategory') : t('addCategory')}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('categoryName')}
              value={formData.name || ''}
              onChange={handleChange('name')}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>{t('parentCategory')}</InputLabel>
              <Select
                value={formData.parent_id || ''}
                onChange={handleChange('parent_id')}
                label={t('parentCategory')}
              >
                <MenuItem value="">لا يوجد (فئة رئيسية)</MenuItem>
                {parentCategories.map(cat => (
                  <MenuItem key={cat.id} value={cat.id}>{cat.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('description')}
              multiline
              rows={3}
              value={formData.description || ''}
              onChange={handleChange('description')}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('image')}
              value={formData.image_url || ''}
              onChange={handleChange('image_url')}
              placeholder="رابط الصورة (اختياري)"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={formData.is_active ? 1 : 0}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 1 }))}
                label="الحالة"
              >
                <MenuItem value={1}>نشط</MenuItem>
                <MenuItem value={0}>غير نشط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" sx={{ fontFamily: 'Cairo, sans-serif' }}>
          {t('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// مكون عرض الفئة كبطاقة
interface CategoryCardProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (id: number) => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, onEdit, onDelete }) => {
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {category.image_url && (
        <CardMedia
          component="img"
          height="140"
          image={category.image_url}
          alt={category.name}
          sx={{ objectFit: 'cover' }}
        />
      )}
      {!category.image_url && (
        <Box
          sx={{
            height: 140,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f1f5f9',
          }}
        >
          <CategoryIcon sx={{ fontSize: 48, color: '#64748b' }} />
        </Box>
      )}
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography variant="h6" sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
            {category.name}
          </Typography>
          <Box>
            <IconButton size="small" onClick={() => onEdit(category)} sx={{ color: '#3b82f6' }}>
              <EditIcon fontSize="small" />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(category.id)} sx={{ color: '#ef4444' }}>
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>

        <Typography variant="body2" sx={{ color: '#64748b', mb: 2, flexGrow: 1 }}>
          {category.description || 'لا يوجد وصف'}
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Chip
            label={category.is_active ? 'نشط' : 'غير نشط'}
            size="small"
            color={category.is_active ? 'success' : 'default'}
          />
          <Typography variant="caption" sx={{ color: '#64748b' }}>
            {category.parts_count || 0} قطعة
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

// مكون عرض الفئات في شكل قائمة هرمية
interface CategoryListProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (id: number) => void;
  level?: number;
}

const CategoryList: React.FC<CategoryListProps> = ({ categories, onEdit, onDelete, level = 0 }) => {
  return (
    <Box>
      {categories.map((category) => (
        <Box key={category.id}>
          <Paper
            sx={{
              p: 2,
              mb: 1,
              ml: level * 3,
              backgroundColor: level > 0 ? '#f8fafc' : 'white',
              border: level > 0 ? '1px solid #e2e8f0' : 'none'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CategoryIcon sx={{ color: '#64748b' }} />
                <Box>
                  <Typography sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
                    {category.name}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b' }}>
                    {category.description || 'لا يوجد وصف'}
                  </Typography>
                </Box>
                <Chip
                  label={`${category.parts_count || 0} قطعة`}
                  size="small"
                  variant="outlined"
                />
                <Chip
                  label={category.is_active ? 'نشط' : 'غير نشط'}
                  size="small"
                  color={category.is_active ? 'success' : 'default'}
                />
              </Box>
              <Box>
                <IconButton size="small" onClick={() => onEdit(category)} sx={{ color: '#3b82f6' }}>
                  <EditIcon fontSize="small" />
                </IconButton>
                <IconButton size="small" onClick={() => onDelete(category.id)} sx={{ color: '#ef4444' }}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </Paper>
          {category.children && category.children.length > 0 && (
            <CategoryList
              categories={category.children}
              onEdit={onEdit}
              onDelete={onDelete}
              level={level + 1}
            />
          )}
        </Box>
      ))}
    </Box>
  );
};

const Categories: React.FC = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'cards' | 'tree'>('cards');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // تحميل البيانات
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بـ API حقيقي
      const mockCategories: Category[] = [
        {
          id: 1,
          name: 'محرك',
          parent_id: null,
          description: 'قطع غيار المحرك وملحقاته',
          image_url: null,
          is_active: true,
          created_at: '2024-01-15',
          updated_at: '2024-05-20',
          parts_count: 45,
          children: [
            {
              id: 11,
              name: 'فلاتر المحرك',
              parent_id: 1,
              description: 'فلاتر الهواء والزيت والوقود',
              image_url: null,
              is_active: true,
              created_at: '2024-01-16',
              updated_at: '2024-05-18',
              parts_count: 15,
            },
            {
              id: 12,
              name: 'قطع المحرك الداخلية',
              parent_id: 1,
              description: 'المكابس والحلقات والصمامات',
              image_url: null,
              is_active: true,
              created_at: '2024-01-17',
              updated_at: '2024-05-17',
              parts_count: 30,
            },
          ],
        },
        {
          id: 2,
          name: 'فرامل',
          parent_id: null,
          description: 'نظام الفرامل وقطع الغيار المتعلقة به',
          image_url: null,
          is_active: true,
          created_at: '2024-01-18',
          updated_at: '2024-05-16',
          parts_count: 28,
          children: [
            {
              id: 21,
              name: 'أقراص الفرامل',
              parent_id: 2,
              description: 'أقراص الفرامل الأمامية والخلفية',
              image_url: null,
              is_active: true,
              created_at: '2024-01-19',
              updated_at: '2024-05-15',
              parts_count: 12,
            },
            {
              id: 22,
              name: 'تيل الفرامل',
              parent_id: 2,
              description: 'تيل الفرامل للعجلات المختلفة',
              image_url: null,
              is_active: true,
              created_at: '2024-01-20',
              updated_at: '2024-05-14',
              parts_count: 16,
            },
          ],
        },
        {
          id: 3,
          name: 'إطارات',
          parent_id: null,
          description: 'الإطارات والعجلات',
          image_url: null,
          is_active: true,
          created_at: '2024-01-21',
          updated_at: '2024-05-13',
          parts_count: 22,
        },
        {
          id: 4,
          name: 'كهرباء',
          parent_id: null,
          description: 'النظام الكهربائي والإلكتروني',
          image_url: null,
          is_active: true,
          created_at: '2024-01-22',
          updated_at: '2024-05-12',
          parts_count: 35,
          children: [
            {
              id: 41,
              name: 'بطاريات',
              parent_id: 4,
              description: 'بطاريات الشاحنات',
              image_url: null,
              is_active: true,
              created_at: '2024-01-23',
              updated_at: '2024-05-11',
              parts_count: 8,
            },
            {
              id: 42,
              name: 'مولدات',
              parent_id: 4,
              description: 'مولدات الكهرباء (الدينامو)',
              image_url: null,
              is_active: true,
              created_at: '2024-01-24',
              updated_at: '2024-05-10',
              parts_count: 12,
            },
            {
              id: 43,
              name: 'إضاءة',
              parent_id: 4,
              description: 'مصابيح وأنوار الشاحنة',
              image_url: null,
              is_active: true,
              created_at: '2024-01-25',
              updated_at: '2024-05-09',
              parts_count: 15,
            },
          ],
        },
        {
          id: 5,
          name: 'تكييف',
          parent_id: null,
          description: 'نظام التكييف والتبريد',
          image_url: null,
          is_active: true,
          created_at: '2024-01-26',
          updated_at: '2024-05-08',
          parts_count: 18,
        },
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      setCategories(mockCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل البيانات', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedCategory(null);
    setDialogOpen(true);
  };

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      try {
        // هنا سيتم حذف الفئة من قاعدة البيانات
        const deleteRecursive = (cats: Category[], targetId: number): Category[] => {
          return cats.filter(cat => {
            if (cat.id === targetId) return false;
            if (cat.children) {
              cat.children = deleteRecursive(cat.children, targetId);
            }
            return true;
          });
        };

        setCategories(prev => deleteRecursive(prev, id));
        setSnackbar({ open: true, message: 'تم حذف الفئة بنجاح', severity: 'success' });
      } catch (error) {
        setSnackbar({ open: true, message: 'خطأ في حذف الفئة', severity: 'error' });
      }
    }
  };

  const handleSave = async (categoryData: Partial<Category>) => {
    try {
      if (selectedCategory) {
        // تحديث فئة موجودة
        const updateRecursive = (cats: Category[], targetId: number, newData: Partial<Category>): Category[] => {
          return cats.map(cat => {
            if (cat.id === targetId) {
              return { ...cat, ...newData, updated_at: new Date().toISOString().split('T')[0] };
            }
            if (cat.children) {
              cat.children = updateRecursive(cat.children, targetId, newData);
            }
            return cat;
          });
        };

        setCategories(prev => updateRecursive(prev, selectedCategory.id, categoryData));
        setSnackbar({ open: true, message: 'تم تحديث الفئة بنجاح', severity: 'success' });
      } else {
        // إضافة فئة جديدة
        const newCategory: Category = {
          id: Date.now(),
          created_at: new Date().toISOString().split('T')[0],
          updated_at: new Date().toISOString().split('T')[0],
          parts_count: 0,
          ...categoryData as Category,
        };

        if (newCategory.parent_id) {
          // إضافة كفئة فرعية
          const addToParent = (cats: Category[], parentId: number, newCat: Category): Category[] => {
            return cats.map(cat => {
              if (cat.id === parentId) {
                return {
                  ...cat,
                  children: [...(cat.children || []), newCat],
                };
              }
              if (cat.children) {
                cat.children = addToParent(cat.children, parentId, newCat);
              }
              return cat;
            });
          };

          setCategories(prev => addToParent(prev, newCategory.parent_id!, newCategory));
        } else {
          // إضافة كفئة رئيسية
          setCategories(prev => [...prev, newCategory]);
        }

        setSnackbar({ open: true, message: 'تم إضافة الفئة بنجاح', severity: 'success' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'خطأ في حفظ الفئة', severity: 'error' });
    }
  };

  // تصفية البيانات
  const filterCategories = (cats: Category[], searchTerm: string): Category[] => {
    return cats.filter(cat => {
      const matchesSearch = cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           cat.description.toLowerCase().includes(searchTerm.toLowerCase());

      if (matchesSearch) return true;

      if (cat.children) {
        const filteredChildren = filterCategories(cat.children, searchTerm);
        if (filteredChildren.length > 0) {
          cat.children = filteredChildren;
          return true;
        }
      }

      return false;
    });
  };

  const filteredCategories = searchTerm ? filterCategories([...categories], searchTerm) : categories;

  // تحويل البيانات الهرمية إلى قائمة مسطحة للعرض كبطاقات
  const flattenCategories = (cats: Category[]): Category[] => {
    const result: Category[] = [];
    cats.forEach(cat => {
      result.push(cat);
      if (cat.children) {
        result.push(...flattenCategories(cat.children));
      }
    });
    return result;
  };

  const flatCategories = flattenCategories(filteredCategories);

  // حساب الإحصائيات
  const totalCategories = flattenCategories(categories).length;
  const activeCategories = flattenCategories(categories).filter(cat => cat.is_active).length;
  const mainCategories = categories.length;
  const totalParts = flattenCategories(categories).reduce((sum, cat) => sum + (cat.parts_count || 0), 0);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontFamily: 'Cairo, sans-serif', fontWeight: 'bold' }}>
        {t('categories')}
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
              {totalCategories}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي الفئات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#10b981', fontWeight: 'bold' }}>
              {activeCategories}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              فئات نشطة
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
              {mainCategories}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              فئات رئيسية
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ color: '#8b5cf6', fontWeight: 'bold' }}>
              {totalParts}
            </Typography>
            <Typography sx={{ fontFamily: 'Cairo, sans-serif', color: '#64748b' }}>
              إجمالي القطع
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* شريط الأدوات */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث في الفئات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: '#64748b', mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>طريقة العرض</InputLabel>
              <Select
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value as 'cards' | 'tree')}
                label="طريقة العرض"
              >
                <MenuItem value="cards">بطاقات</MenuItem>
                <MenuItem value="tree">قائمة هرمية</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {t('addCategory')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* عرض المحتوى */}
      {viewMode === 'cards' ? (
        <Grid container spacing={3}>
          {flatCategories.map(category => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={category.id}>
              <CategoryCard
                category={category}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <CategoryList
          categories={filteredCategories}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      )}

      {/* نموذج إضافة/تعديل */}
      <CategoryDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        category={selectedCategory}
        categories={categories}
        onSave={handleSave}
      />

      {/* رسائل التنبيه */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} sx={{ width: '100%' }}>
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {snackbar.message}
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Categories;
