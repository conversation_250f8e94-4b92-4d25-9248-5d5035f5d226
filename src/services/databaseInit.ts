// Database Initialization Service
// خدمة تهيئة قاعدة البيانات

import { ElectronAPI } from '../preload';

export class DatabaseInitService {
  private static instance: DatabaseInitService;
  private electronAPI: ElectronAPI;

  private constructor() {
    this.electronAPI = (window as any).electronAPI;
  }

  public static getInstance(): DatabaseInitService {
    if (!DatabaseInitService.instance) {
      DatabaseInitService.instance = new DatabaseInitService();
    }
    return DatabaseInitService.instance;
  }

  // تهيئة قاعدة البيانات مع البيانات التجريبية
  async initializeDatabase(): Promise<void> {
    try {
      console.log('Initializing database with sample data...');

      // إنشاء الجداول الأساسية (إذا لم تكن موجودة)
      await this.createTables();

      // إدراج البيانات التجريبية
      await this.insertSampleData();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    // إنشاء جدول الفئات
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS categories (
        category_id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_name TEXT NOT NULL UNIQUE,
        category_name_en TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_category_id INTEGER,
        category_image_path TEXT,
        display_order INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_category_id) REFERENCES categories(category_id) ON DELETE SET NULL
      )
    `);

    // إنشاء جدول الموردين
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS suppliers (
        supplier_id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        supplier_rating INTEGER,
        average_lead_time_days INTEGER,
        payment_terms_agreed TEXT,
        bank_account_details TEXT,
        tax_id_number TEXT,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء جدول قطع الغيار
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS parts (
        part_id INTEGER PRIMARY KEY AUTOINCREMENT,
        part_number TEXT NOT NULL UNIQUE,
        part_name TEXT NOT NULL,
        part_name_en TEXT NOT NULL,
        category_id INTEGER,
        description TEXT,
        purchase_price REAL NOT NULL,
        selling_price REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER NOT NULL DEFAULT 5,
        barcode TEXT UNIQUE,
        shelf_location TEXT,
        reorder_point INTEGER,
        preferred_supplier_id INTEGER,
        last_stock_check_date TEXT,
        is_active INTEGER DEFAULT 1 CHECK(is_active IN (0, 1)),
        weight_kg REAL,
        dimensions_cm TEXT,
        alternative_part_numbers TEXT,
        image_path TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE SET NULL,
        FOREIGN KEY (preferred_supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL
      )
    `);

    // إنشاء جدول العملاء
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS customers (
        customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT UNIQUE,
        address TEXT,
        city TEXT,
        country TEXT,
        customer_type TEXT CHECK(customer_type IN ('individual', 'company', 'workshop', 'fleet_owner')),
        loyalty_points INTEGER DEFAULT 0,
        last_purchase_date TEXT,
        total_spent_amount REAL DEFAULT 0.00,
        credit_limit REAL DEFAULT 0.00,
        tax_id_number TEXT,
        account_manager_id INTEGER,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء جدول فواتير المبيعات
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS sales_invoices (
        invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        invoice_number TEXT NOT NULL UNIQUE,
        invoice_date TEXT NOT NULL,
        total_amount REAL NOT NULL,
        discount_amount REAL DEFAULT 0.00,
        tax_amount REAL DEFAULT 0.00,
        final_amount REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0.00,
        payment_status TEXT NOT NULL CHECK(payment_status IN ('paid', 'partial', 'unpaid')),
        shipping_address_details TEXT,
        shipping_method_id INTEGER,
        shipping_tracking_number TEXT,
        promotion_code_applied TEXT,
        sales_channel TEXT CHECK(sales_channel IN ('in_store', 'online_website', 'phone_order', 'social_media')),
        original_invoice_id_for_return INTEGER,
        notes TEXT,
        user_id INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE RESTRICT
      )
    `);

    // إنشاء جدول الديون
    await this.electronAPI.dbRun(`
      CREATE TABLE IF NOT EXISTS Debts (
        debt_id INTEGER PRIMARY KEY AUTOINCREMENT,
        debt_type TEXT NOT NULL CHECK(debt_type IN ('receivable', 'payable')),
        customer_id INTEGER,
        supplier_id INTEGER,
        related_sales_invoice_id INTEGER,
        related_purchase_invoice_id INTEGER,
        debt_reference_number TEXT UNIQUE,
        debt_description TEXT,
        principal_amount REAL NOT NULL,
        currency_code TEXT DEFAULT 'DZD',
        interest_rate_annual_percent REAL DEFAULT 0.0,
        interest_calculation_method TEXT CHECK(interest_calculation_method IN ('simple', 'compound', 'none')) DEFAULT 'none',
        compounding_frequency TEXT CHECK(compounding_frequency IN ('daily', 'monthly', 'quarterly', 'annually', 'none')) DEFAULT 'none',
        interest_accrued REAL DEFAULT 0.0,
        total_debt_amount_due REAL NOT NULL,
        amount_paid REAL DEFAULT 0.0,
        remaining_balance REAL NOT NULL,
        issue_date TEXT NOT NULL DEFAULT (date('now')),
        due_date TEXT NOT NULL,
        payment_terms_details TEXT,
        status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('pending_approval', 'active', 'partially_paid', 'fully_paid', 'overdue', 'disputed', 'written_off', 'cancelled')),
        last_payment_date TEXT,
        user_id_created INTEGER,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL,
        FOREIGN KEY (related_sales_invoice_id) REFERENCES sales_invoices(invoice_id) ON DELETE SET NULL,
        CONSTRAINT chk_debt_party_link CHECK ( (customer_id IS NOT NULL AND supplier_id IS NULL) OR (customer_id IS NULL AND supplier_id IS NOT NULL) )
      )
    `);

    // إنشاء الفهارس المحسنة
    await this.createIndexes();
  }

  private async createIndexes(): Promise<void> {
    const indexes = [
      // Parts indexes
      'CREATE INDEX IF NOT EXISTS idx_parts_part_number ON parts(part_number)',
      'CREATE INDEX IF NOT EXISTS idx_parts_part_name ON parts(part_name)',
      'CREATE INDEX IF NOT EXISTS idx_parts_barcode ON parts(barcode) WHERE barcode IS NOT NULL',
      'CREATE INDEX IF NOT EXISTS idx_parts_quantity_min_quantity ON parts(quantity, min_quantity)',
      'CREATE INDEX IF NOT EXISTS idx_parts_is_active_category ON parts(is_active, category_id)',
      'CREATE INDEX IF NOT EXISTS idx_parts_search_composite ON parts(part_name, part_number, barcode)',

      // Customer indexes
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)',
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_type_city ON customers(customer_type, city)',

      // Sales indexes
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer_id ON sales_invoices(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_date_status ON sales_invoices(invoice_date, payment_status)',
      'CREATE INDEX IF NOT EXISTS idx_sales_invoices_invoice_number ON sales_invoices(invoice_number)',

      // Debt indexes
      'CREATE INDEX IF NOT EXISTS idx_debts_customer_id ON Debts(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_debts_supplier_id ON Debts(supplier_id)',
      'CREATE INDEX IF NOT EXISTS idx_debts_debt_type_status ON Debts(debt_type, status)',
      'CREATE INDEX IF NOT EXISTS idx_debts_due_date ON Debts(due_date)',
    ];

    for (const indexQuery of indexes) {
      await this.electronAPI.dbRun(indexQuery);
    }
  }

  private async insertSampleData(): Promise<void> {
    // التحقق من وجود البيانات
    const existingParts = await this.electronAPI.dbQuery('SELECT COUNT(*) as count FROM parts');
    if (existingParts[0].count > 0) {
      console.log('Sample data already exists, skipping insertion');
      return;
    }

    // إدراج الفئات
    await this.insertCategories();

    // إدراج الموردين
    await this.insertSuppliers();

    // إدراج العملاء
    await this.insertCustomers();

    // إدراج قطع الغيار
    await this.insertParts();

    // إدراج فواتير المبيعات
    await this.insertSalesInvoices();

    // إدراج الديون
    await this.insertDebts();
  }

  private async insertCategories(): Promise<void> {
    const categories = [
      ['محرك', 'Engine', 'قطع غيار المحرك وملحقاته'],
      ['فرامل', 'Brakes', 'نظام الفرامل والأقراص والأحذية'],
      ['إطارات', 'Tires', 'الإطارات والعجلات والجنوط'],
      ['كهرباء', 'Electrical', 'النظام الكهربائي والبطاريات'],
      ['تكييف', 'Air Conditioning', 'نظام التكييف والتبريد'],
      ['ناقل الحركة', 'Transmission', 'ناقل الحركة والقابض'],
      ['التعليق', 'Suspension', 'نظام التعليق والممتصات'],
      ['الهيكل', 'Body', 'هيكل الشاحنة والكابينة'],
      ['الوقود', 'Fuel System', 'نظام الوقود والحقن'],
      ['العادم', 'Exhaust', 'نظام العادم والكاتم'],
      ['التوجيه', 'Steering', 'نظام التوجيه والمقود'],
      ['الإضاءة', 'Lighting', 'أنظمة الإضاءة والمصابيح'],
      ['الزيوت', 'Oils & Fluids', 'الزيوت والسوائل'],
      ['الفلاتر', 'Filters', 'جميع أنواع الفلاتر'],
      ['أدوات', 'Tools', 'أدوات الصيانة والإصلاح'],
    ];

    for (const [nameAr, nameEn, description] of categories) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO categories (category_name, category_name_en, description, display_order) VALUES (?, ?, ?, ?)',
        [nameAr, nameEn, description, categories.indexOf([nameAr, nameEn, description]) + 1]
      );
    }
  }

  private async insertSuppliers(): Promise<void> {
    const suppliers = [
      ['شركة قطع الغيار المتقدمة', 'أحمد محمد', '0123456789', '<EMAIL>', 'شارع الصناعة 123', 'الجزائر', 'الجزائر', 5, 7, 'نقد 30 يوم'],
      ['مؤسسة الشاحنات الحديثة', 'فاطمة علي', '0987654321', '<EMAIL>', 'طريق القاهرة 456', 'الإسكندرية', 'مصر', 4, 10, 'نقد 15 يوم'],
      ['شركة الإمداد السريع', 'محمد حسن', '0555123456', '<EMAIL>', 'شارع الملك فهد 789', 'الرياض', 'السعودية', 4, 5, 'نقد فوري'],
      ['مجموعة النقل الثقيل', 'سارة أحمد', '0777888999', '<EMAIL>', 'المنطقة الصناعية الثانية', 'الدار البيضاء', 'المغرب', 5, 14, 'آجل 45 يوم'],
      ['شركة المحركات الأوروبية', 'يوسف علي', '0666777888', '<EMAIL>', 'شارع التجارة 45', 'تونس', 'تونس', 4, 21, 'آجل 60 يوم'],
      ['مؤسسة الفرامل المتخصصة', 'نادية حسن', '0555666777', '<EMAIL>', 'طريق المطار 67', 'عمان', 'الأردن', 5, 3, 'نقد فوري'],
      ['شركة الإطارات الدولية', 'خالد محمود', '0444555666', '<EMAIL>', 'شارع الملك عبدالله 89', 'الكويت', 'الكويت', 4, 7, 'نقد 30 يوم'],
      ['مجموعة الكهرباء والإلكترونيات', 'ليلى عبدالله', '0333444555', '<EMAIL>', 'المدينة الصناعية', 'دبي', 'الإمارات', 5, 5, 'نقد 15 يوم'],
      ['شركة التكييف المتقدم', 'عمر سالم', '0222333444', '<EMAIL>', 'شارع الصناعات 12', 'الدوحة', 'قطر', 4, 10, 'آجل 30 يوم'],
      ['مؤسسة قطع الغيار الأصلية', 'هدى محمد', '0111222333', '<EMAIL>', 'طريق الملك فيصل 34', 'المنامة', 'البحرين', 5, 7, 'نقد 20 يوم'],
    ];

    for (const [name, contact, phone, email, address, city, country, rating, leadTime, terms] of suppliers) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO suppliers (supplier_name, contact_person, phone, email, address, city, country, supplier_rating, average_lead_time_days, payment_terms_agreed) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [name, contact, phone, email, address, city, country, rating, leadTime, terms]
      );
    }
  }

  private async insertCustomers(): Promise<void> {
    const customers = [
      // عملاء أفراد
      ['أحمد محمد علي', null, '0123456789', '<EMAIL>', 'شارع الاستقلال 123', 'الجزائر', 'الجزائر', 'individual', 150, 125000, 30000],
      ['فاطمة حسن', null, '0234567890', '<EMAIL>', 'حي النصر 45', 'وهران', 'الجزائر', 'individual', 80, 67000, 20000],
      ['محمد عبدالله', null, '0345678901', '<EMAIL>', 'شارع المدينة 67', 'قسنطينة', 'الجزائر', 'individual', 200, 180000, 40000],
      ['سارة أحمد', null, '0456789012', '<EMAIL>', 'طريق الجامعة 89', 'سطيف', 'الجزائر', 'individual', 45, 32000, 15000],
      ['يوسف محمود', null, '0567890123', '<EMAIL>', 'شارع الثورة 12', 'عنابة', 'الجزائر', 'individual', 120, 95000, 25000],

      // شركات النقل
      ['شركة النقل السريع', 'محمد علي', '0987654321', '<EMAIL>', 'المنطقة الصناعية الأولى', 'القاهرة', 'مصر', 'company', 500, 850000, 200000],
      ['مؤسسة الشحن الدولي', 'أحمد سالم', '0876543210', '<EMAIL>', 'ميناء الإسكندرية', 'الإسكندرية', 'مصر', 'company', 750, 1200000, 300000],
      ['شركة النقل البري', 'خالد حسن', '0765432109', '<EMAIL>', 'طريق السويس 234', 'القاهرة', 'مصر', 'company', 300, 450000, 150000],
      ['مجموعة اللوجستيات المتقدمة', 'نادية محمد', '0654321098', '<EMAIL>', 'المدينة الصناعية', 'الرياض', 'السعودية', 'company', 600, 980000, 250000],
      ['شركة الشحن العربي', 'عمر عبدالله', '0543210987', '<EMAIL>', 'ميناء جدة', 'جدة', 'السعودية', 'company', 400, 650000, 180000],

      // ورش الصيانة
      ['ورشة الإخوة للصيانة', 'سعد أحمد', '0555123456', '<EMAIL>', 'شارع الصناعات 56', 'الرياض', 'السعودية', 'workshop', 250, 320000, 100000],
      ['مركز الصيانة المتخصص', 'هشام علي', '0444234567', '<EMAIL>', 'المنطقة الصناعية الثالثة', 'الدمام', 'السعودية', 'workshop', 180, 240000, 80000],
      ['ورشة النجوم للإصلاح', 'طارق محمد', '0333345678', '<EMAIL>', 'شارع التجارة 78', 'الكويت', 'الكويت', 'workshop', 320, 420000, 120000],
      ['مركز الخليج للصيانة', 'ماجد سالم', '0222456789', '<EMAIL>', 'المنطقة الحرة', 'دبي', 'الإمارات', 'workshop', 450, 580000, 150000],
      ['ورشة الأطلس المتقدمة', 'رشيد حسن', '0111567890', '<EMAIL>', 'الحي الصناعي', 'الدار البيضاء', 'المغرب', 'workshop', 200, 280000, 90000],

      // أصحاب أساطيل
      ['أسطول النقل الذهبي', 'عبدالرحمن محمد', '0999888777', '<EMAIL>', 'طريق الملك فهد 123', 'الرياض', 'السعودية', 'fleet_owner', 800, 1500000, 400000],
      ['مجموعة الشاحنات الحديثة', 'سليمان علي', '0888777666', '<EMAIL>', 'شارع الصناعة 45', 'جدة', 'السعودية', 'fleet_owner', 650, 1200000, 350000],
      ['أسطول الصحراء للنقل', 'بدر أحمد', '0777666555', '<EMAIL>', 'المنطقة اللوجستية', 'الدوحة', 'قطر', 'fleet_owner', 900, 1800000, 500000],
      ['شركة الأساطيل المتحدة', 'وليد حسن', '0666555444', '<EMAIL>', 'ميناء الشارقة', 'الشارقة', 'الإمارات', 'fleet_owner', 750, 1350000, 380000],
      ['مؤسسة النقل الكبرى', 'فيصل محمود', '0555444333', '<EMAIL>', 'المدينة الاقتصادية', 'المنامة', 'البحرين', 'fleet_owner', 550, 950000, 280000],
    ];

    for (const [name, contact, phone, email, address, city, country, type, points, spent, credit] of customers) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO customers (customer_name, contact_person, phone, email, address, city, country, customer_type, loyalty_points, total_spent_amount, credit_limit) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [name, contact, phone, email, address, city, country, type, points, spent, credit]
      );
    }
  }

  private async insertParts(): Promise<void> {
    const parts = [
      // قطع المحرك (category_id: 1)
      ['ENG001', 'فلتر زيت محرك', 'Engine Oil Filter', 1, '1234567890123', 1500, 2000, 25, 10, 'A1-01', 1],
      ['ENG002', 'فلتر وقود', 'Fuel Filter', 1, '1234567890124', 2200, 2800, 15, 8, 'A1-02', 1],
      ['ENG003', 'فلتر هواء محرك', 'Engine Air Filter', 1, '1234567890125', 3500, 4500, 20, 12, 'A1-03', 1],
      ['ENG004', 'حشية رأس المحرك', 'Head Gasket', 1, '1234567890126', 8500, 11000, 8, 5, 'A1-04', 2],
      ['ENG005', 'مضخة مياه', 'Water Pump', 1, '1234567890127', 12000, 15500, 6, 4, 'A1-05', 2],
      ['ENG006', 'ترموستات', 'Thermostat', 1, '1234567890128', 1800, 2400, 30, 15, 'A1-06', 1],
      ['ENG007', 'حزام توقيت', 'Timing Belt', 1, '1234567890129', 4500, 6000, 12, 8, 'A1-07', 3],
      ['ENG008', 'مضخة زيت', 'Oil Pump', 1, '1234567890130', 18000, 23000, 4, 3, 'A1-08', 2],

      // قطع الفرامل (category_id: 2)
      ['BRK001', 'أقراص فرامل أمامية', 'Front Brake Discs', 2, '2345678901234', 3000, 4000, 5, 8, 'B2-01', 2],
      ['BRK002', 'أقراص فرامل خلفية', 'Rear Brake Discs', 2, '2345678901235', 2800, 3700, 8, 6, 'B2-02', 2],
      ['BRK003', 'تيل فرامل أمامي', 'Front Brake Pads', 2, '2345678901236', 1500, 2000, 0, 10, 'B2-03', 6],
      ['BRK004', 'تيل فرامل خلفي', 'Rear Brake Pads', 2, '2345678901237', 1300, 1800, 15, 12, 'B2-04', 6],
      ['BRK005', 'أسطوانة فرامل رئيسية', 'Master Brake Cylinder', 2, '2345678901238', 8500, 11000, 3, 5, 'B2-05', 6],
      ['BRK006', 'خرطوم فرامل', 'Brake Hose', 2, '2345678901239', 800, 1200, 25, 15, 'B2-06', 6],
      ['BRK007', 'سائل فرامل', 'Brake Fluid', 2, '2345678901240', 450, 650, 50, 20, 'B2-07', 6],

      // الإطارات (category_id: 3)
      ['TIR001', 'إطار 315/80R22.5', 'Tire 315/80R22.5', 3, '3456789012345', 25000, 32000, 12, 5, 'C1-01', 7],
      ['TIR002', 'إطار 295/80R22.5', 'Tire 295/80R22.5', 3, '3456789012346', 23000, 30000, 8, 4, 'C1-02', 7],
      ['TIR003', 'إطار 385/65R22.5', 'Tire 385/65R22.5', 3, '3456789012347', 28000, 36000, 6, 3, 'C1-03', 7],
      ['TIR004', 'جنط فولاذي 22.5', 'Steel Rim 22.5', 3, '3456789012348', 8500, 11500, 15, 8, 'C1-04', 7],
      ['TIR005', 'صمام إطار', 'Tire Valve', 3, '3456789012349', 150, 250, 100, 50, 'C1-05', 7],
      ['TIR006', 'رقعة إطار', 'Tire Patch', 3, '3456789012350', 80, 150, 200, 100, 'C1-06', 7],

      // الكهرباء (category_id: 4)
      ['ELC001', 'بطارية 12V 100Ah', 'Battery 12V 100Ah', 4, '4567890123456', 15000, 20000, 2, 5, 'D1-01', 8],
      ['ELC002', 'بطارية 12V 180Ah', 'Battery 12V 180Ah', 4, '4567890123457', 22000, 28000, 4, 3, 'D1-02', 8],
      ['ELC003', 'دينامو', 'Alternator', 4, '4567890123458', 18000, 24000, 3, 2, 'D1-03', 8],
      ['ELC004', 'سلف', 'Starter Motor', 4, '4567890123459', 16000, 21000, 2, 2, 'D1-04', 8],
      ['ELC005', 'مصباح أمامي LED', 'LED Headlight', 4, '4567890123460', 3500, 4800, 12, 8, 'D1-05', 8],
      ['ELC006', 'مصباح خلفي', 'Tail Light', 4, '4567890123461', 1200, 1800, 20, 10, 'D1-06', 8],
      ['ELC007', 'فيوز 30A', 'Fuse 30A', 4, '4567890123462', 25, 50, 150, 100, 'D1-07', 8],

      // التكييف (category_id: 5)
      ['AIR001', 'فلتر هواء كابينة', 'Cabin Air Filter', 5, '5678901234567', 800, 1200, 0, 10, 'E2-01', 9],
      ['AIR002', 'كمبروسر تكييف', 'AC Compressor', 5, '5678901234568', 35000, 45000, 2, 2, 'E2-02', 9],
      ['AIR003', 'مكثف تكييف', 'AC Condenser', 5, '5678901234569', 12000, 16000, 3, 2, 'E2-03', 9],
      ['AIR004', 'مبخر تكييف', 'AC Evaporator', 5, '5678901234570', 15000, 20000, 2, 2, 'E2-04', 9],
      ['AIR005', 'غاز تكييف R134a', 'AC Gas R134a', 5, '5678901234571', 450, 700, 25, 15, 'E2-05', 9],

      // ناقل الحركة (category_id: 6)
      ['TRN001', 'زيت ناقل حركة', 'Transmission Oil', 6, '6789012345678', 2200, 3000, 30, 20, 'F3-01', 1],
      ['TRN002', 'قابض', 'Clutch Kit', 6, '6789012345679', 25000, 32000, 3, 2, 'F3-02', 5],
      ['TRN003', 'فلتر ناقل حركة', 'Transmission Filter', 6, '6789012345680', 1800, 2500, 8, 5, 'F3-03', 5],
      ['TRN004', 'حشية ناقل حركة', 'Transmission Gasket', 6, '6789012345681', 3500, 4800, 5, 3, 'F3-04', 5],

      // التعليق (category_id: 7)
      ['SUS001', 'ممتص صدمات أمامي', 'Front Shock Absorber', 7, '7890123456789', 8500, 11500, 6, 4, 'G4-01', 3],
      ['SUS002', 'ممتص صدمات خلفي', 'Rear Shock Absorber', 7, '7890123456790', 7500, 10000, 8, 5, 'G4-02', 3],
      ['SUS003', 'نابض أمامي', 'Front Spring', 7, '7890123456791', 4500, 6200, 10, 6, 'G4-03', 3],
      ['SUS004', 'نابض خلفي', 'Rear Spring', 7, '7890123456792', 5200, 7000, 8, 5, 'G4-04', 3],
      ['SUS005', 'كرة مفصل', 'Ball Joint', 7, '7890123456793', 1800, 2500, 15, 10, 'G4-05', 3],

      // الهيكل (category_id: 8)
      ['BOD001', 'مرآة جانبية', 'Side Mirror', 8, '8901234567890', 2500, 3500, 12, 8, 'H5-01', 4],
      ['BOD002', 'مقبض باب', 'Door Handle', 8, '8901234567891', 800, 1200, 20, 12, 'H5-02', 4],
      ['BOD003', 'زجاج أمامي', 'Windshield', 8, '8901234567892', 18000, 24000, 2, 2, 'H5-03', 4],
      ['BOD004', 'مساحة زجاج', 'Windshield Wiper', 8, '8901234567893', 450, 700, 25, 15, 'H5-04', 4],

      // نظام الوقود (category_id: 9)
      ['FUE001', 'خزان وقود', 'Fuel Tank', 9, '9012345678901', 45000, 58000, 2, 1, 'I6-01', 1],
      ['FUE002', 'مضخة وقود', 'Fuel Pump', 9, '9012345678902', 12000, 16000, 4, 3, 'I6-02', 1],
      ['FUE003', 'حاقن وقود', 'Fuel Injector', 9, '9012345678903', 8500, 11500, 6, 4, 'I6-03', 1],
      ['FUE004', 'خرطوم وقود', 'Fuel Hose', 9, '9012345678904', 350, 550, 30, 20, 'I6-04', 1],

      // نظام العادم (category_id: 10)
      ['EXH001', 'كاتم صوت', 'Muffler', 10, '0123456789012', 8500, 11500, 5, 3, 'J7-01', 2],
      ['EXH002', 'أنبوب عادم', 'Exhaust Pipe', 10, '0123456789013', 3500, 4800, 8, 5, 'J7-02', 2],
      ['EXH003', 'محول حفاز', 'Catalytic Converter', 10, '0123456789014', 25000, 32000, 2, 2, 'J7-03', 2],
    ];

    for (const [partNum, nameAr, nameEn, catId, barcode, purchasePrice, sellPrice, qty, minQty, location, supplierId] of parts) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO parts (part_number, part_name, part_name_en, category_id, barcode, purchase_price, selling_price, quantity, min_quantity, shelf_location, preferred_supplier_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [partNum, nameAr, nameEn, catId, barcode, purchasePrice, sellPrice, qty, minQty, location, supplierId, 1]
      );
    }
  }

  private async insertSalesInvoices(): Promise<void> {
    const invoices = [
      // فواتير مدفوعة
      [1, 'INV-2024-001', '2024-05-28', 32000, 0, 0, 32000, 32000, 'paid', 'in_store'],
      [6, 'INV-2024-004', '2024-05-25', 125000, 5000, 0, 120000, 120000, 'paid', 'in_store'],
      [11, 'INV-2024-005', '2024-05-26', 85000, 0, 0, 85000, 85000, 'paid', 'phone_order'],
      [16, 'INV-2024-006', '2024-05-27', 450000, 20000, 0, 430000, 430000, 'paid', 'in_store'],
      [3, 'INV-2024-007', '2024-05-24', 67000, 2000, 0, 65000, 65000, 'paid', 'online_website'],
      [8, 'INV-2024-008', '2024-05-23', 280000, 10000, 0, 270000, 270000, 'paid', 'in_store'],
      [13, 'INV-2024-009', '2024-05-22', 95000, 0, 0, 95000, 95000, 'paid', 'phone_order'],
      [18, 'INV-2024-010', '2024-05-21', 1200000, 50000, 0, 1150000, 1150000, 'paid', 'in_store'],
      [5, 'INV-2024-011', '2024-05-20', 45000, 0, 0, 45000, 45000, 'paid', 'social_media'],
      [10, 'INV-2024-012', '2024-05-19', 320000, 15000, 0, 305000, 305000, 'paid', 'in_store'],

      // فواتير غير مدفوعة
      [2, 'INV-2024-002', '2024-05-29', 4000, 200, 0, 3800, 0, 'unpaid', 'phone_order'],
      [7, 'INV-2024-013', '2024-05-30', 180000, 0, 0, 180000, 0, 'unpaid', 'in_store'],
      [12, 'INV-2024-014', '2024-05-31', 75000, 3000, 0, 72000, 0, 'unpaid', 'online_website'],
      [17, 'INV-2024-015', '2024-06-01', 520000, 0, 0, 520000, 0, 'unpaid', 'phone_order'],
      [4, 'INV-2024-016', '2024-06-02', 28000, 0, 0, 28000, 0, 'unpaid', 'in_store'],
      [9, 'INV-2024-017', '2024-06-03', 350000, 15000, 0, 335000, 0, 'unpaid', 'in_store'],
      [14, 'INV-2024-018', '2024-06-04', 125000, 5000, 0, 120000, 0, 'unpaid', 'social_media'],
      [19, 'INV-2024-019', '2024-06-05', 890000, 40000, 0, 850000, 0, 'unpaid', 'in_store'],

      // فواتير مدفوعة جزئياً
      [3, 'INV-2024-003', '2024-05-30', 20000, 0, 0, 20000, 10000, 'partial', 'in_store'],
      [15, 'INV-2024-020', '2024-06-06', 480000, 20000, 0, 460000, 200000, 'partial', 'in_store'],
      [20, 'INV-2024-021', '2024-06-07', 750000, 30000, 0, 720000, 300000, 'partial', 'phone_order'],
      [6, 'INV-2024-022', '2024-06-08', 95000, 0, 0, 95000, 50000, 'partial', 'in_store'],
      [11, 'INV-2024-023', '2024-06-09', 160000, 8000, 0, 152000, 80000, 'partial', 'online_website'],
      [16, 'INV-2024-024', '2024-06-10', 380000, 15000, 0, 365000, 150000, 'partial', 'in_store'],
      [1, 'INV-2024-025', '2024-06-11', 55000, 2000, 0, 53000, 25000, 'partial', 'social_media'],
      [8, 'INV-2024-026', '2024-06-12', 220000, 10000, 0, 210000, 100000, 'partial', 'in_store'],
      [13, 'INV-2024-027', '2024-06-13', 140000, 5000, 0, 135000, 70000, 'partial', 'phone_order'],
      [18, 'INV-2024-028', '2024-06-14', 680000, 25000, 0, 655000, 300000, 'partial', 'in_store'],
      [2, 'INV-2024-029', '2024-06-15', 85000, 0, 0, 85000, 40000, 'partial', 'online_website'],
      [7, 'INV-2024-030', '2024-06-16', 195000, 8000, 0, 187000, 90000, 'partial', 'in_store'],
    ];

    for (const [custId, invNum, date, total, discount, tax, final, paid, status, channel] of invoices) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO sales_invoices (customer_id, invoice_number, invoice_date, total_amount, discount_amount, tax_amount, final_amount, paid_amount, payment_status, sales_channel, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [custId, invNum, date, total, discount, tax, final, paid, status, channel, 1]
      );
    }
  }

  private async insertDebts(): Promise<void> {
    const debts = [
      // ديون نشطة (غير مدفوعة)
      ['receivable', 2, null, 2, 'DEBT-2024-001', 'دين من فاتورة INV-2024-002', 3800, 3800, 0, 3800, '2024-05-29', '2024-06-15', 'active'],
      ['receivable', 7, null, 13, 'DEBT-2024-003', 'دين من فاتورة INV-2024-013', 180000, 180000, 0, 180000, '2024-05-30', '2024-06-30', 'active'],
      ['receivable', 12, null, 14, 'DEBT-2024-004', 'دين من فاتورة INV-2024-014', 72000, 72000, 0, 72000, '2024-05-31', '2024-07-01', 'active'],
      ['receivable', 17, null, 15, 'DEBT-2024-005', 'دين من فاتورة INV-2024-015', 520000, 520000, 0, 520000, '2024-06-01', '2024-07-15', 'active'],
      ['receivable', 4, null, 16, 'DEBT-2024-006', 'دين من فاتورة INV-2024-016', 28000, 28000, 0, 28000, '2024-06-02', '2024-07-02', 'active'],
      ['receivable', 9, null, 17, 'DEBT-2024-007', 'دين من فاتورة INV-2024-017', 335000, 335000, 0, 335000, '2024-06-03', '2024-07-18', 'active'],
      ['receivable', 14, null, 18, 'DEBT-2024-008', 'دين من فاتورة INV-2024-018', 120000, 120000, 0, 120000, '2024-06-04', '2024-07-04', 'active'],
      ['receivable', 19, null, 19, 'DEBT-2024-009', 'دين من فاتورة INV-2024-019', 850000, 850000, 0, 850000, '2024-06-05', '2024-08-05', 'active'],

      // ديون مدفوعة جزئياً
      ['receivable', 3, null, 3, 'DEBT-2024-002', 'دين من فاتورة INV-2024-003', 20000, 20000, 10000, 10000, '2024-05-30', '2024-06-30', 'partially_paid'],
      ['receivable', 15, null, 20, 'DEBT-2024-010', 'دين من فاتورة INV-2024-020', 460000, 460000, 200000, 260000, '2024-06-06', '2024-08-06', 'partially_paid'],
      ['receivable', 20, null, 21, 'DEBT-2024-011', 'دين من فاتورة INV-2024-021', 720000, 720000, 300000, 420000, '2024-06-07', '2024-08-07', 'partially_paid'],
      ['receivable', 6, null, 22, 'DEBT-2024-012', 'دين من فاتورة INV-2024-022', 95000, 95000, 50000, 45000, '2024-06-08', '2024-07-08', 'partially_paid'],
      ['receivable', 11, null, 23, 'DEBT-2024-013', 'دين من فاتورة INV-2024-023', 152000, 152000, 80000, 72000, '2024-06-09', '2024-07-24', 'partially_paid'],
      ['receivable', 16, null, 24, 'DEBT-2024-014', 'دين من فاتورة INV-2024-024', 365000, 365000, 150000, 215000, '2024-06-10', '2024-08-10', 'partially_paid'],
      ['receivable', 1, null, 25, 'DEBT-2024-015', 'دين من فاتورة INV-2024-025', 53000, 53000, 25000, 28000, '2024-06-11', '2024-07-11', 'partially_paid'],
      ['receivable', 8, null, 26, 'DEBT-2024-016', 'دين من فاتورة INV-2024-026', 210000, 210000, 100000, 110000, '2024-06-12', '2024-08-12', 'partially_paid'],
      ['receivable', 13, null, 27, 'DEBT-2024-017', 'دين من فاتورة INV-2024-027', 135000, 135000, 70000, 65000, '2024-06-13', '2024-07-28', 'partially_paid'],
      ['receivable', 18, null, 28, 'DEBT-2024-018', 'دين من فاتورة INV-2024-028', 655000, 655000, 300000, 355000, '2024-06-14', '2024-08-14', 'partially_paid'],
      ['receivable', 2, null, 29, 'DEBT-2024-019', 'دين من فاتورة INV-2024-029', 85000, 85000, 40000, 45000, '2024-06-15', '2024-07-15', 'partially_paid'],
      ['receivable', 7, null, 30, 'DEBT-2024-020', 'دين من فاتورة INV-2024-030', 187000, 187000, 90000, 97000, '2024-06-16', '2024-08-16', 'partially_paid'],

      // ديون متأخرة
      ['receivable', 5, null, null, 'DEBT-2024-021', 'دين قديم - صيانة شاملة', 85000, 85000, 0, 85000, '2024-04-15', '2024-05-15', 'overdue'],
      ['receivable', 12, null, null, 'DEBT-2024-022', 'دين قديم - قطع غيار متنوعة', 125000, 125000, 30000, 95000, '2024-04-10', '2024-05-10', 'overdue'],
      ['receivable', 17, null, null, 'DEBT-2024-023', 'دين قديم - إطارات وفرامل', 320000, 320000, 100000, 220000, '2024-03-20', '2024-04-20', 'overdue'],
      ['receivable', 9, null, null, 'DEBT-2024-024', 'دين قديم - محرك وناقل حركة', 450000, 450000, 0, 450000, '2024-03-15', '2024-04-15', 'overdue'],
      ['receivable', 14, null, null, 'DEBT-2024-025', 'دين قديم - كهرباء وتكييف', 180000, 180000, 50000, 130000, '2024-04-01', '2024-05-01', 'overdue'],
      ['receivable', 19, null, null, 'DEBT-2024-026', 'دين قديم - صيانة أسطول', 750000, 750000, 200000, 550000, '2024-02-28', '2024-03-30', 'overdue'],
      ['receivable', 4, null, null, 'DEBT-2024-027', 'دين قديم - قطع متفرقة', 65000, 65000, 0, 65000, '2024-04-05', '2024-05-05', 'overdue'],
      ['receivable', 11, null, null, 'DEBT-2024-028', 'دين قديم - هيكل وتعليق', 95000, 95000, 25000, 70000, '2024-03-25', '2024-04-25', 'overdue'],

      // ديون للموردين (مستحقة الدفع)
      ['payable', null, 1, null, 'DEBT-2024-029', 'مستحقات شركة قطع الغيار المتقدمة', 250000, 250000, 150000, 100000, '2024-05-15', '2024-06-15', 'partially_paid'],
      ['payable', null, 2, null, 'DEBT-2024-030', 'مستحقات مؤسسة الشاحنات الحديثة', 180000, 180000, 0, 180000, '2024-06-01', '2024-07-01', 'active'],
      ['payable', null, 3, null, 'DEBT-2024-031', 'مستحقات شركة الإمداد السريع', 320000, 320000, 320000, 0, '2024-05-20', '2024-06-20', 'fully_paid'],
      ['payable', null, 6, null, 'DEBT-2024-032', 'مستحقات مؤسسة الفرامل المتخصصة', 95000, 95000, 0, 95000, '2024-06-10', '2024-07-10', 'active'],
      ['payable', null, 7, null, 'DEBT-2024-033', 'مستحقات شركة الإطارات الدولية', 450000, 450000, 200000, 250000, '2024-05-25', '2024-06-25', 'partially_paid'],
      ['payable', null, 8, null, 'DEBT-2024-034', 'مستحقات مجموعة الكهرباء والإلكترونيات', 125000, 125000, 0, 125000, '2024-06-05', '2024-07-20', 'active'],
    ];

    for (const [type, custId, suppId, invoiceId, refNum, desc, principal, total, paid, remaining, issueDate, dueDate, status] of debts) {
      await this.electronAPI.dbRun(
        'INSERT OR IGNORE INTO Debts (debt_type, customer_id, supplier_id, related_sales_invoice_id, debt_reference_number, debt_description, principal_amount, total_debt_amount_due, amount_paid, remaining_balance, issue_date, due_date, status, user_id_created) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [type, custId, suppId, invoiceId, refNum, desc, principal, total, paid, remaining, issueDate, dueDate, status, 1]
      );
    }
  }
}

export const dbInitService = DatabaseInitService.getInstance();
