import { contextBridge, ipcRenderer } from 'electron';

// تعريف واجهة API للتطبيق
export interface ElectronAPI {
  // عمليات قاعدة البيانات
  dbQuery: (sql: string, params?: any[]) => Promise<any[]>;
  dbRun: (sql: string, params?: any[]) => Promise<any>;
  
  // عمليات الملفات
  selectFile: () => Promise<string | null>;
  saveFile: (content: string, filename: string) => Promise<boolean>;
  
  // معلومات النظام
  getVersion: () => string;
  getPlatform: () => string;
}

// كشف API للعملية الرئيسية
const electronAPI: ElectronAPI = {
  // عمليات قاعدة البيانات
  dbQuery: (sql: string, params?: any[]) => ipcRenderer.invoke('db-query', sql, params),
  dbRun: (sql: string, params?: any[]) => ipcRenderer.invoke('db-run', sql, params),
  
  // عمليات الملفات
  selectFile: () => ipcRenderer.invoke('select-file'),
  saveFile: (content: string, filename: string) => ipcRenderer.invoke('save-file', content, filename),
  
  // معلومات النظام
  getVersion: () => process.versions.electron,
  getPlatform: () => process.platform,
};

// كشف API للنافذة
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// تعريف النوع للنافذة العامة
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
