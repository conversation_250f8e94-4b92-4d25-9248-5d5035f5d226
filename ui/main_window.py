# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window

النافذة الرئيسية لنظام إدارة قطع غيار الشاحنات
Main window for Truck Parts Management System
"""

import logging
from typing import Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QMenuBar, QStatusBar, QToolBar, QAction, QLabel,
    QFrame, QSplitter, QTabWidget, QMessageBox,
    QPushButton, QGridLayout, QGroupBox, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QPixmap, QFont

from database.connection import DatabaseManager
from config.settings import AppSettings
from utils.arabic_utils import set_widget_rtl, get_arabic_font, reshape_arabic_text
from utils.helpers import format_currency, format_date

class MainWindow(QMainWindow):
    """
    النافذة الرئيسية للتطبيق
    Main Application Window Class
    """
    
    # إشارات مخصصة
    # Custom signals
    status_message = pyqtSignal(str)
    theme_changed = pyqtSignal(str)
    
    def __init__(self, db_manager: DatabaseManager, settings: AppSettings):
        super().__init__()
        
        # المتغيرات الأساسية
        # Basic variables
        self.db_manager = db_manager
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الواجهة
        # UI variables
        self.central_widget = None
        self.menu_bar = None
        self.tool_bar = None
        self.status_bar = None
        self.dashboard_widget = None
        self.tab_widget = None
        
        # إعداد النافذة
        # Setup window
        self.setup_window()
        self.setup_ui()
        self.setup_connections()
        self.load_window_state()
        
        # تحديث البيانات
        # Update data
        self.refresh_dashboard()
        
        self.logger.info("تم إنشاء النافذة الرئيسية - Main window created")
    
    def setup_window(self):
        """إعداد خصائص النافذة - Setup window properties"""
        # عنوان النافذة
        # Window title
        self.setWindowTitle("نظام إدارة قطع غيار الشاحنات - Truck Parts Management System")
        
        # حجم النافذة
        # Window size
        width = self.settings.get("window", "width", 1200)
        height = self.settings.get("window", "height", 800)
        self.resize(width, height)
        
        # موقع النافذة
        # Window position
        pos_x = self.settings.get("window", "position_x", 100)
        pos_y = self.settings.get("window", "position_y", 100)
        self.move(pos_x, pos_y)
        
        # حالة النافذة
        # Window state
        if self.settings.get("window", "maximized", False):
            self.showMaximized()
        
        # أيقونة النافذة
        # Window icon
        self.setup_window_icon()
        
        # تعيين التخطيط العربي
        # Set Arabic layout
        set_widget_rtl(self)
    
    def setup_window_icon(self):
        """إعداد أيقونة النافذة - Setup window icon"""
        try:
            # محاولة تحميل الأيقونة من الملف
            # Try to load icon from file
            icon_path = "resources/icons/app_icon.png"
            icon = QIcon(icon_path)
            
            if not icon.isNull():
                self.setWindowIcon(icon)
            else:
                # استخدام أيقونة افتراضية
                # Use default icon
                self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        except Exception as e:
            self.logger.warning(f"تعذر تحميل أيقونة النافذة - Could not load window icon: {e}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم - Setup user interface"""
        # إنشاء شريط القوائم
        # Create menu bar
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        # Create tool bar
        self.create_tool_bar()
        
        # إنشاء شريط الحالة
        # Create status bar
        self.create_status_bar()
        
        # إنشاء الواجهة المركزية
        # Create central widget
        self.create_central_widget()
        
        # تطبيق النمط
        # Apply theme
        self.apply_theme()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم - Create menu bar"""
        self.menu_bar = self.menuBar()
        
        # قائمة الملف
        # File menu
        file_menu = self.menu_bar.addMenu("ملف")
        
        # إجراءات قائمة الملف
        # File menu actions
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        restore_action = QAction("استعادة", self)
        restore_action.triggered.connect(self.restore_backup)
        file_menu.addAction(restore_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المخزون
        # Inventory menu
        inventory_menu = self.menu_bar.addMenu("المخزون")
        
        parts_action = QAction("قطع الغيار", self)
        parts_action.triggered.connect(self.show_parts_management)
        inventory_menu.addAction(parts_action)
        
        categories_action = QAction("الفئات", self)
        categories_action.triggered.connect(self.show_categories_management)
        inventory_menu.addAction(categories_action)
        
        stock_action = QAction("حركة المخزون", self)
        stock_action.triggered.connect(self.show_stock_movements)
        inventory_menu.addAction(stock_action)
        
        # قائمة المبيعات
        # Sales menu
        sales_menu = self.menu_bar.addMenu("المبيعات")
        
        new_sale_action = QAction("بيع جديد", self)
        new_sale_action.setShortcut("F2")
        new_sale_action.triggered.connect(self.new_sale)
        sales_menu.addAction(new_sale_action)
        
        invoices_action = QAction("الفواتير", self)
        invoices_action.triggered.connect(self.show_invoices)
        sales_menu.addAction(invoices_action)
        
        # قائمة العملاء
        # Customers menu
        customers_menu = self.menu_bar.addMenu("العملاء")
        
        customers_action = QAction("إدارة العملاء", self)
        customers_action.triggered.connect(self.show_customers_management)
        customers_menu.addAction(customers_action)
        
        # قائمة الموردين
        # Suppliers menu
        suppliers_menu = self.menu_bar.addMenu("الموردين")
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(self.show_suppliers_management)
        suppliers_menu.addAction(suppliers_action)
        
        # قائمة التقارير
        # Reports menu
        reports_menu = self.menu_bar.addMenu("التقارير")
        
        sales_report_action = QAction("تقرير المبيعات", self)
        sales_report_action.triggered.connect(self.show_sales_report)
        reports_menu.addAction(sales_report_action)
        
        inventory_report_action = QAction("تقرير المخزون", self)
        inventory_report_action.triggered.connect(self.show_inventory_report)
        reports_menu.addAction(inventory_report_action)
        
        # قائمة الأدوات
        # Tools menu
        tools_menu = self.menu_bar.addMenu("أدوات")
        
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # قائمة المساعدة
        # Help menu
        help_menu = self.menu_bar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """إنشاء شريط الأدوات - Create tool bar"""
        self.tool_bar = self.addToolBar("الأدوات الرئيسية")
        
        # أزرار شريط الأدوات
        # Toolbar buttons
        new_sale_btn = QPushButton("بيع جديد")
        new_sale_btn.clicked.connect(self.new_sale)
        self.tool_bar.addWidget(new_sale_btn)
        
        self.tool_bar.addSeparator()
        
        parts_btn = QPushButton("قطع الغيار")
        parts_btn.clicked.connect(self.show_parts_management)
        self.tool_bar.addWidget(parts_btn)
        
        customers_btn = QPushButton("العملاء")
        customers_btn.clicked.connect(self.show_customers_management)
        self.tool_bar.addWidget(customers_btn)
        
        self.tool_bar.addSeparator()
        
        reports_btn = QPushButton("التقارير")
        reports_btn.clicked.connect(self.show_reports_menu)
        self.tool_bar.addWidget(reports_btn)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة - Create status bar"""
        self.status_bar = self.statusBar()
        
        # رسالة الحالة
        # Status message
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # شريط التقدم
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # معلومات المستخدم
        # User info
        self.user_label = QLabel("المستخدم: مدير النظام")
        self.status_bar.addPermanentWidget(self.user_label)
        
        # الوقت
        # Time
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # مؤقت لتحديث الوقت
        # Timer for time update
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # كل ثانية - Every second
        
        self.update_time()
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية - Create central widget"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # تخطيط رئيسي
        # Main layout
        main_layout = QVBoxLayout(self.central_widget)
        
        # إنشاء لوحة المعلومات
        # Create dashboard
        self.create_dashboard()
        main_layout.addWidget(self.dashboard_widget)
        
        # إنشاء علامات التبويب
        # Create tabs
        self.create_tab_widget()
        main_layout.addWidget(self.tab_widget)
        
        # تعيين التخطيط العربي
        # Set Arabic layout
        set_widget_rtl(self.central_widget)
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات - Create dashboard"""
        self.dashboard_widget = QGroupBox("لوحة المعلومات")
        dashboard_layout = QGridLayout(self.dashboard_widget)
        
        # بطاقات المعلومات
        # Info cards
        self.create_info_cards(dashboard_layout)
        
        # تعيين الخط العربي
        # Set Arabic font
        arabic_font = get_arabic_font(12, bold=True)
        self.dashboard_widget.setFont(arabic_font)
    
    def create_info_cards(self, layout):
        """إنشاء بطاقات المعلومات - Create info cards"""
        # بطاقة إجمالي المبيعات
        # Total sales card
        sales_card = self.create_info_card("إجمالي المبيعات اليوم", "0.00 د.ج", "#4CAF50")
        layout.addWidget(sales_card, 0, 0)
        
        # بطاقة عدد الفواتير
        # Invoices count card
        invoices_card = self.create_info_card("عدد الفواتير اليوم", "0", "#2196F3")
        layout.addWidget(invoices_card, 0, 1)
        
        # بطاقة المخزون المنخفض
        # Low stock card
        low_stock_card = self.create_info_card("مخزون منخفض", "0", "#FF9800")
        layout.addWidget(low_stock_card, 0, 2)
        
        # بطاقة العملاء الجدد
        # New customers card
        customers_card = self.create_info_card("عملاء جدد هذا الشهر", "0", "#9C27B0")
        layout.addWidget(customers_card, 0, 3)
    
    def create_info_card(self, title: str, value: str, color: str) -> QFrame:
        """إنشاء بطاقة معلومات - Create info card"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 10px;
                color: white;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        # العنوان
        # Title
        title_label = QLabel(title)
        title_label.setFont(get_arabic_font(10))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        # Value
        value_label = QLabel(value)
        value_label.setFont(get_arabic_font(16, bold=True))
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
    
    def create_tab_widget(self):
        """إنشاء علامات التبويب - Create tab widget"""
        self.tab_widget = QTabWidget()
        
        # علامة تبويب المبيعات السريعة
        # Quick sales tab
        quick_sales_tab = QWidget()
        self.tab_widget.addTab(quick_sales_tab, "مبيعات سريعة")
        
        # علامة تبويب البحث
        # Search tab
        search_tab = QWidget()
        self.tab_widget.addTab(search_tab, "البحث")
        
        # علامة تبويب التقارير السريعة
        # Quick reports tab
        reports_tab = QWidget()
        self.tab_widget.addTab(reports_tab, "تقارير سريعة")
    
    def setup_connections(self):
        """إعداد الاتصالات - Setup connections"""
        # ربط الإشارات
        # Connect signals
        self.status_message.connect(self.show_status_message)
        self.theme_changed.connect(self.apply_theme)
    
    def apply_theme(self, theme_name: str = None):
        """تطبيق النمط - Apply theme"""
        if theme_name is None:
            theme_name = self.settings.get("general", "theme", "light")
        
        if theme_name == "dark":
            self.apply_dark_theme()
        else:
            self.apply_light_theme()
    
    def apply_light_theme(self):
        """تطبيق النمط الفاتح - Apply light theme"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
                color: #333333;
            }
            QMenuBar {
                background-color: #ffffff;
                border-bottom: 1px solid #cccccc;
            }
            QToolBar {
                background-color: #ffffff;
                border: 1px solid #cccccc;
            }
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #cccccc;
            }
        """)
    
    def apply_dark_theme(self):
        """تطبيق النمط المظلم - Apply dark theme"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QMenuBar {
                background-color: #3c3c3c;
                border-bottom: 1px solid #555555;
            }
            QToolBar {
                background-color: #3c3c3c;
                border: 1px solid #555555;
            }
            QStatusBar {
                background-color: #3c3c3c;
                border-top: 1px solid #555555;
            }
        """)
    
    def load_window_state(self):
        """تحميل حالة النافذة - Load window state"""
        # يمكن إضافة منطق تحميل حالة النافذة هنا
        # Window state loading logic can be added here
        pass
    
    def save_window_state(self):
        """حفظ حالة النافذة - Save window state"""
        try:
            # حفظ حجم النافذة
            # Save window size
            self.settings.set("window", "width", self.width())
            self.settings.set("window", "height", self.height())
            
            # حفظ موقع النافذة
            # Save window position
            self.settings.set("window", "position_x", self.x())
            self.settings.set("window", "position_y", self.y())
            
            # حفظ حالة التكبير
            # Save maximized state
            self.settings.set("window", "maximized", self.isMaximized())
            
            # حفظ الإعدادات
            # Save settings
            self.settings.save_settings()
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ حالة النافذة - Window state saving error: {e}")
    
    def refresh_dashboard(self):
        """تحديث لوحة المعلومات - Refresh dashboard"""
        # يمكن إضافة منطق تحديث البيانات هنا
        # Data refresh logic can be added here
        pass
    
    def update_time(self):
        """تحديث الوقت - Update time"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def show_status_message(self, message: str):
        """عرض رسالة الحالة - Show status message"""
        self.status_label.setText(message)
    
    # أحداث القوائم - Menu events
    def new_file(self):
        """ملف جديد - New file"""
        self.show_status_message("ملف جديد")
    
    def open_file(self):
        """فتح ملف - Open file"""
        self.show_status_message("فتح ملف")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية - Create backup"""
        self.show_status_message("جاري إنشاء نسخة احتياطية...")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية - Restore backup"""
        self.show_status_message("استعادة نسخة احتياطية")
    
    def new_sale(self):
        """بيع جديد - New sale"""
        self.show_status_message("بيع جديد")
    
    def show_parts_management(self):
        """عرض إدارة قطع الغيار - Show parts management"""
        self.show_status_message("إدارة قطع الغيار")
    
    def show_categories_management(self):
        """عرض إدارة الفئات - Show categories management"""
        self.show_status_message("إدارة الفئات")
    
    def show_stock_movements(self):
        """عرض حركة المخزون - Show stock movements"""
        self.show_status_message("حركة المخزون")
    
    def show_invoices(self):
        """عرض الفواتير - Show invoices"""
        self.show_status_message("الفواتير")
    
    def show_customers_management(self):
        """عرض إدارة العملاء - Show customers management"""
        self.show_status_message("إدارة العملاء")
    
    def show_suppliers_management(self):
        """عرض إدارة الموردين - Show suppliers management"""
        self.show_status_message("إدارة الموردين")
    
    def show_sales_report(self):
        """عرض تقرير المبيعات - Show sales report"""
        self.show_status_message("تقرير المبيعات")
    
    def show_inventory_report(self):
        """عرض تقرير المخزون - Show inventory report"""
        self.show_status_message("تقرير المخزون")
    
    def show_reports_menu(self):
        """عرض قائمة التقارير - Show reports menu"""
        self.show_status_message("قائمة التقارير")
    
    def show_settings(self):
        """عرض الإعدادات - Show settings"""
        self.show_status_message("الإعدادات")
    
    def show_about(self):
        """عرض حول البرنامج - Show about"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            "نظام إدارة قطع غيار الشاحنات\nالإصدار 1.0.0\n\nتم تطويره بواسطة فريق إدارة قطع الغيار"
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة - Window close event"""
        # حفظ حالة النافذة
        # Save window state
        self.save_window_state()
        
        # قبول الإغلاق
        # Accept close
        event.accept()
        
        self.logger.info("تم إغلاق النافذة الرئيسية - Main window closed")
